import React from 'react';
import {
  Mail, Phone, MapPin, UserCheck, Users, Building,
  MoreVertical
} from 'lucide-react';
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  CardDescription
} from '@/components/cards/card';
import { Contact } from '@/lib/types/contacts';
import useLanguage from "@/hooks/useLanguage";


interface ContactCardProps {
  contact: Contact;
  onSelect: (contact: Contact) => void;
  isSelected?: boolean;
}

const ContactCard: React.FC<ContactCardProps> = ({
  contact,
  onSelect,
  isSelected = false
}) => {
  const { t } = useLanguage();

  return (
    <Card
      onClick={() => onSelect(contact)}
      className={`cursor-pointer transition-all duration-200 hover:shadow-md  dark:bg-boxdark lg:translate-x-0 
        ${isSelected
          ? 'border-blue-500 bg-blue-50'
          : 'border-gray-200 hover:border-blue-300'
        }`}
    >
      <CardHeader className="pb-2">
        <div className="flex justify-between items-start w-full">
          <div className="flex flex-col">
            <CardTitle className="text-lg flex items-center gap-2">
              {contact.name}
              {contact.type.includes('client') && (
                <UserCheck size={16} className="text-green-500" />
              )}
              {contact.type.includes('agency') && (
                <Users size={16} className="text-blue-500" />
              )}
            </CardTitle>
            <CardDescription className="flex items-center gap-2">
              <Building size={14} className="text-gray-400" />
              {contact.company}
            </CardDescription>
          </div>

          <div className="text-right transform hover:scale-105 transition-transform duration-200">
            <p className="text-sm font-medium text-gray-500 mb-1">{t("balance")}</p>
            {(() => {
              const invertedBalance = (contact.balance || 0) * -1;
              let colorClass = "text-blue-600 dark:text-blue-400";
              if (invertedBalance > 0) colorClass = "text-green-600 dark:text-green-400";
              else if (invertedBalance < 0) colorClass = "text-red-600 dark:text-red-400";
              return (
                <p className={`text-2xl font-bold tracking-tight ${colorClass}`}>
                  <span style={{ direction: 'ltr', unicodeBidi: 'isolate' }} className="inline-flex">
                    {invertedBalance > 0 ? '+' : invertedBalance < 0 ? '-' : ''}
                    {Math.abs(invertedBalance).toLocaleString('en-US', {
                      minimumFractionDigits: 0,
                      maximumFractionDigits: 0
                    })} {t("EGP")}
                  </span>
                </p>
              );
            })()}
          </div>
        </div>
      </CardHeader>

      <CardContent>
        {/* Desktop/Large Screen Layout */}
        {contact.email || contact.phone || contact.address ? (
        <div className="hidden md:grid grid-cols-3 gap-4 text-sm text-gray-600">
          { contact.email && (
          <div className="flex items-center gap-2">
            <Mail size={16} className="text-blue-500" />
            {contact.email}
          </div>
          )}
          {contact.phone && (
          <div className="flex items-center gap-2">
            <Phone size={16} className="text-green-500" />
            {contact.phone}
          </div>
          )}
          {contact.address && (
            <div className="flex items-center gap-2">
              <MapPin size={16} className="text-red-500" />
              {contact.address}
            </div>
          )}
        </div>
        ) : (
          <></>
        )}

        {/* Mobile/Small Screen Layout */}
        <div className="md:hidden flex flex-col space-y-1 text-sm text-gray-600">
          {contact.email && (
            <div className="flex items-center gap-2">
              <Mail size={14} className="text-blue-500" />
              <span className="truncate max-w-[200px]">{contact.email}</span>
            </div>
          )}
          {contact.phone && (
            <div className="flex items-center gap-2">
              <Phone size={14} className="text-green-500" />
              {contact.phone}
            </div>
          )}
        </div>

        {/* Location Summary - Always Visible */}
        <div className="mt-2 text-xs text-gray-500">
          {contact.sharedLocations && contact.sharedLocations.length > 0 && (
            <div>
              {t('shared')} : {contact.sharedLocations.slice(0, 2).map(loc => loc.name).join(', ')}
              {contact.sharedLocations.length > 2 && ` +${contact.sharedLocations.length - 2}`}
            </div>
          )}
          {contact.ownedLocations && contact.ownedLocations.length > 0 && (
            <div>
              {t('owned')} : {contact.ownedLocations.slice(0, 2).map(loc => loc.name).join(', ')}
              {contact.ownedLocations.length > 2 && ` +${contact.ownedLocations.length - 2}`}
            </div>
          )}
        </div>

      </CardContent>
    </Card>
  );
};

export default ContactCard;
