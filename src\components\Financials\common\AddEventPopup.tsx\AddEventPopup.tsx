import React, { useEffect, useState } from "react";
import { EventDetails, Contact, Location } from "@/lib/interfaces/finaces";
import useLanguage from "@/hooks/useLanguage";
import { FaTimes, FaPlus } from "react-icons/fa";
import { <PERSON><PERSON> } from "@/components/ui/button";
import EventForm from "./EventForm";
import ContactForm from "./ContactForm";
import LocationForm from "./LocationForm";
import AddTypePopup from "./AddTypePopup";
import { getRecurringDateRange } from "./RecurringDateRanges";
import { useContacts } from "@/hooks/useContact";
import { useLocations } from "@/hooks/useLocations";
import {
  useExpenseTypes,
  useExpenseTypeServices,
} from "@/hooks/useExpensesTypes";
import { useIncomeTypes, useIncomeTypeServices } from "@/hooks/useIncomeTypes";
import { LoadingComp } from "@/components/common/Loading";
import GroupEventForm from "./GroupEventForm";
import IncomeServices from "@/lib/income";
import ExpenseServices from "@/lib/expenses";
import { SuccessPopup } from "@/components/common/successPopUp";
import { ErrorPopup } from "@/components/common/errorPopUp";
import { DataImporter } from "@/components/Importer/DataImporter"; // Import DataImporter

interface AddEventPopupProps {
  onClose: () => void;
  onSave: (events: EventDetails[]) => void;
  defaultCategory: "income" | "expense";
}

const AddEventPopup: React.FC<AddEventPopupProps> = ({
  onClose,
  onSave,
  defaultCategory = "income",
}) => {
  const { t, language } = useLanguage();
  const isRTL = language === "ar";
  const [title, setTitle] = useState("");
  const [amount, setAmount] = useState<number>(0);
  const [dueDate, setDueDate] = useState("");
  const [notificationDate, setNotificationDate] = useState("");
  const [category, setCategory] = useState<"income" | "expense">(
    defaultCategory,
  );
  const [type, setType] = useState<string>("");
  const [status, setStatus] = useState<
    "completed" | "pending" | "cancelled" | "upcoming" | "overdue"
  >("upcoming");
  const [isRecursive, setIsRecursive] = useState(false);
  const [eventMode, setEventMode] = useState<"single" | "group" | "import">(
    "single",
  );
  const [loading, setLoading] = useState(false);
  const [recurrenceRange, setRecurrenceRange] = useState<string>("thisMonth");
  const [recurrenceCount, setRecurrenceCount] = useState<number>(1);
  const [selectedContact, setSelectedContact] = useState<Contact | null>(null);
  const [selectedLocation, setSelectedLocation] = useState<Location | null>(
    null,
  );
  const [showContactForm, setShowContactForm] = useState(false);
  const [showLocationForm, setShowLocationForm] = useState(false);
  const [showAddTypePopup, setShowAddTypePopup] = useState(false);
  const { data: contactsData, isLoading, isError } = useContacts();
  const {
    data: locationsData,
    isLoading: isLocationsLoading,
    isError: isLocationsError,
  } = useLocations();
  const { data: incomeTypes, isLoading: isIncomeTypesLoading } =
    useIncomeTypes();
  const { data: expensesTypes, isLoading: isExpensesTypesLoading } =
    useExpenseTypes();
  const [autoCreate, setAutoCreate] = useState<boolean>(true);
  const [isTotalAmount, setIsTotalAmount] = useState<boolean>(true);

  const [priority, setPriority] = useState<string>("medium");
  const [typeId, setTypeId] = useState<string>("");
  const [parentTitle, setParentTitle] = useState<string>("");
  const [parentAmount, setParentAmount] = useState<number>(0);
  const IncomeService = IncomeServices();
  const ExpenseService = ExpenseServices();
  const [parentDescription, setParentDescription] = useState<string>("");
  const [children, setChildren] = useState<
    Array<{
      amount: number;
      due_date: string;
      notification_view_date: string;
    }>
  >([]);
  const [groupTypes, setGroupTypes] = useState<{
    [key: string]: { id: string; name: string }[];
  }>({
    income: [],
    expense: [],
  });
  const [showSuccess, setShowSuccess] = useState(false);
  const [showError, setShowError] = useState(false);
  const [successMessage, setSuccessMessage] = useState("");
  const [errorMessage, setErrorMessage] = useState("");
  const [importedData, setImportedData] = useState<EventDetails[]>([]); // State for imported data

  const handleImport = (data: any) => {
    console.log("Imported data:", data);
    // map the data and make the amount into a number and then set the imported data
    const formattedData = data.map((item: any) => {
      const contact = item.contact
        ? contactsData?.contacts.find(
            (contact: Contact) => contact.name === item.contact,
          )
        : null;
      const location = item.location
        ? locationsData?.locations.find(
            (location: Location) => location.name === item.location,
          )
        : null;

      return {
        ...item,
        amount: parseFloat(item.amount) || 0, // Ensure amount is a number
        dueDate: item.dueDate ? new Date(item.dueDate).toISOString() : null,
        notificationDate: item.notificationDate
          ? new Date(item.notificationDate).toISOString()
          : null,
        category: item.category || "expense", // Default to expense if not provided
        type: item.type || "",
        status: item.status || "upcoming",
        priority: item.priority || "medium",
        contact: contact,
        location: location,
      };
    });
    setImportedData(formattedData);
  };

  const handleSaveImportedData = async () => {
    try {
      console.log("Saving imported data:", importedData);

      for (const entry of importedData) {
        console.log("Processing entry:", entry);

        if (entry.category === "income") {
          await IncomeService.createIncome({
            title: entry.title,
            amount: entry.amount,
            received_date: null,
            due_date: entry.dueDate,
            status: entry.status,
            priority: entry.priority,
            contact_id: entry.contact?.id || "",
            location_id: entry.location?.id || "",
            description: entry.description || "",
            notification_date: entry.notificationDate || null,
            autoCreate: false,
            isTotalAmount: true,
          });
        } else {
          await ExpenseService.createExpense({
            title: entry.title,
            amount: entry.amount,
            paid_date: null,
            due_date: entry.dueDate,
            status: entry.status,
            priority: entry.priority,
            contact_id: entry.contact?.id || "",
            location_id: entry.location?.id || "",
            description: entry.description || "",
            notification_date: entry.notificationDate || null,
            autoCreate: false,
            isTotalAmount: true,
          });
        }
      }
      setShowSuccess(true);
      setSuccessMessage(t("Imported data saved successfully!"));
      setTimeout(() => {
        setShowSuccess(false);
        window.location.reload();
      }, 2000);
    } catch (err) {
      setShowError(true);
      setErrorMessage(t("An error occurred while saving the imported data."));
      setTimeout(() => setShowError(false), 3000);
    }
  };

  // reformat Location Data to the current model
  const formattedLocations = locationsData?.locations.map((location: any) => ({
    id: location.id || "default-id",
    name: location.name,
    address: location.address,
    city: location.city ? location.city : "",
    country: location.country ? location.country : "",
    postalCode: location.postalCode ? location.postalCode : "",
    percentage: location.ourPercentage ? location.ourPercentage : 0,
    amount: location.amount ? location.amount : 0,
    areWeOwners: location.areWeOwner ? location.areWeOwner : false,
  }));
  const [locationList, setLocationList] = useState<Location[]>(
    formattedLocations || [],
  );

  useEffect(() => {
    if (locationsData) {
      setLocationList(formattedLocations || []);
    }
  }, [locationsData]);
  useEffect(() => {
    if (locationsData) {
      setLocationList(formattedLocations || []);
    }
  }, [locationsData]);

  const [types, setTypes] = useState<{ [key: string]: string[] }>({
    income: [],
    expense: [],
  });

  useEffect(() => {
    setTypes({
      income: incomeTypes?.income_types.map((type: any) => type.name) || [],
      expense: expensesTypes?.event_types.map((type: any) => type.name) || [],
    });
    setGroupTypes({
      income:
        incomeTypes?.income_types.map((type: any) => ({
          id: type.id,
          name: type.name,
        })) || [],
      expense:
        expensesTypes?.event_types.map((type: any) => ({
          id: type.id,
          name: type.name,
        })) || [],
    });
  }, [expensesTypes, incomeTypes]);

  const handleSaveParent = async () => {
    const parentEvent = {
      title: parentTitle,
      amount: parentAmount,
      description: parentDescription,
      due_date: null,
      notification_view_date: null,
      priority,
      type_id: typeId,
      category,
      parent: {
        title: parentTitle,
        amount: parentAmount,
        description: parentDescription,
      },
      children: children.map((child) => ({
        amount: child.amount,
        due_date: child.due_date,
        notification_view_date: child.notification_view_date,
      })),
    };
    if (parentEvent.category === "income") {
      await IncomeService.createParentIncome({
        title: parentTitle,
        amount: parentAmount,
        description: parentDescription,
        due_date: null,
        notification_view_date: null,
        priority,
        type_id: typeId,
        parent: {
          title: parentTitle,
          amount: parentAmount,
          description: parentDescription,
        },
        children: children.map((child) => ({
          amount: child.amount,
          due_date: child.due_date,
          notification_view_date: child.notification_view_date,
        })),
      });
    } else {
      await ExpenseService.createParentExpense({
        title: parentTitle,
        amount: parentAmount,
        description: parentDescription,
        due_date: null,
        notification_view_date: null,
        priority,
        type_id: typeId,
        parent: {
          title: parentTitle,
          amount: parentAmount,
          description: parentDescription,
        },
        children: children.map((child) => ({
          amount: child.amount,
          due_date: child.due_date,
          notification_view_date: child.notification_view_date,
        })),
      });
    }
  };

  const handleSaveNormal = async (
    shouldAutoCreate: boolean,
    isTotalAmount: boolean,
  ) => {
    const events: EventDetails[] = [];
    const { start, end } = getRecurringDateRange(recurrenceRange);
    const startDate = new Date(start);
    const endDate = new Date(end);
    // if we are not the location owners , the shouldAutoCrete should be false
    let finalAutoCreate = shouldAutoCreate;
    if (!selectedLocation?.areWeOwners) {
      finalAutoCreate = false;
    }
    for (let i = 0; i < (isRecursive ? recurrenceCount : 1); i++) {
      const eventDate = new Date(startDate);
      eventDate.setDate(
        startDate.getDate() +
          i *
            Math.floor(
              (endDate.getTime() - startDate.getTime()) /
                recurrenceCount /
                (1000 * 60 * 60 * 24),
            ),
      );

      if (eventDate > endDate) break;

      const newEvent = {
        title,
        amount,
        dueDate: eventDate.toISOString(),
        category,
        type,
        status,
        lastEdited: new Date().toISOString(),
        lastEditedBy: "Admin",
        priority,
        editingHistory: [],
        contact: selectedContact || {
          id: "default-id",
          name: "",
          email: "",
          phone: "",
        },
        location: selectedLocation || {
          id: "default-id",
          name: "",
          address: "",
          city: "",
          country: "",
          postalCode: "",
        },
        notificationDate: notificationDate || null,
      };

      events.push({
        id: (Date.now() + i).toString(),
        category,
        type,
        title,
        amount,
        dueDate: eventDate.toISOString(),
        status,
        lastEdited: new Date().toISOString(),
        lastEditedBy: "Admin",
        priority: "medium",
        editingHistory: [],
        contact: selectedContact || {
          id: "default-id",
          name: "",
          email: "",
          phone: "",
        },
        location: selectedLocation || {
          id: "default-id",
          name: "",
          address: "",
          city: "",
          country: "",
          postalCode: "",
        },
        notificationDate: notificationDate || "",
      });

      if (newEvent.category === "income") {
        await IncomeService.createIncome({
          title,
          amount: amount,
          received_date: null,
          due_date: eventDate.toISOString(),
          status,
          priority: "medium",
          contact_id: selectedContact ? selectedContact.id : "",
          location_id: selectedLocation ? selectedLocation.id : "",
          description: parentDescription,
          notification_date: notificationDate || null,
          autoCreate: finalAutoCreate,
          isTotalAmount: isTotalAmount,
        });
      } else {
        await ExpenseService.createExpense({
          title,
          amount: amount,
          paid_date: null,
          due_date: eventDate.toISOString(),
          status,
          priority: "medium",
          contact_id: selectedContact ? selectedContact.id : "",
          location_id: selectedLocation ? selectedLocation.id : "",
          description: parentDescription,
          notification_date: notificationDate || null,
          autoCreate: finalAutoCreate,
          isTotalAmount: isTotalAmount,
        });
      }
    }
  };

  const proceedWithSave = async (
    shouldAutoCreate: boolean,
    isTotalAmount: boolean,
  ) => {
    setLoading(true);
    try {
      if (eventMode === "single") {
        await handleSaveNormal(shouldAutoCreate, isTotalAmount);
      } else {
        await handleSaveParent();
      }
      setShowSuccess(true);
      setSuccessMessage(t("Event saved successfully!"));
      setTimeout(() => {
        setShowSuccess(false);
        window.location.reload();
      }, 2000);
    } catch (err) {
      setShowError(true);
      setErrorMessage(t("An error occurred while saving the event."));
      setTimeout(() => setShowError(false), 3000);
    }
    setLoading(false);
  };

  const handleSave = async () => {
    await proceedWithSave(autoCreate, isTotalAmount);
  };

  const handleAddType = () => {
    setShowAddTypePopup(true);
  };

  const handleSaveType = (newType: string) => {
    if (!newType) return;
    const categorycat = category === "income" ? "income" : "expense";
    if (categorycat === "income") {
      const incomeTypeServices = useIncomeTypeServices();
      incomeTypeServices.createIncomeType({ name: newType });
    } else {
      const expenseTypeServices = useExpenseTypeServices();
      expenseTypeServices.createExpenseType({ name: newType });
    }
    setTypes((prevTypes) => ({
      ...prevTypes,
      [category]: [...prevTypes[category], newType],
    }));
    setType(newType);
    setShowAddTypePopup(false);
  };

  const contacts = contactsData?.contacts || [];
  useEffect(() => {
    if (contacts.length > 0) {
      setSelectedContact(contacts[0]);
    }
    if (locationList.length > 0) {
      setSelectedLocation(locationList[0]);
    }
  }, [contacts, locationList]);

  if (isLoading || isLocationsLoading || isExpensesTypesLoading || loading) {
    return (
      <div className="fixed inset-0 z-[1000] flex items-center justify-center bg-black bg-opacity-50 p-4">
        <div
          className={`animate-fade-in relative max-h-[80vh] w-full max-w-[95vw] overflow-y-auto rounded-xl border border-gray-300 bg-white p-6 shadow-xl dark:border-gray-700 dark:bg-gray-900 md:max-h-[90vh] md:max-w-4xl md:p-8 ${
            isRTL ? "lg:mr-72.5" : "lg:ml-72.5"
          }`}
        >
          <LoadingComp />
        </div>
      </div>
    );
  }

  return (
    <div className="fixed inset-0 z-[1000] flex items-center justify-center bg-black bg-opacity-50 p-4">
      <div
        className={`animate-fade-in [80vh] relative w-full max-w-[95vw] overflow-y-auto rounded-xl border border-gray-300 bg-white p-6 shadow-xl dark:border-gray-700 dark:bg-gray-900 md:h-[90vh] md:max-w-4xl md:p-8 ${
          isRTL ? "lg:mr-72.5" : "lg:ml-72.5"
        }`}
      >
        <button
          className={`absolute top-4 ${isRTL ? "left-4" : "right-4"} text-gray-600 hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-100`}
          onClick={onClose}
        >
          <FaTimes size={24} />
        </button>

        <div className="mx-auto mb-6 flex w-full max-w-3xl gap-6">
          {["single", "group", "import"].map((mode) => (
            <div
              key={mode}
              onClick={() =>
                setEventMode(mode as "single" | "group" | "import")
              }
              className={`relative flex-1 cursor-pointer rounded-md py-4 text-center text-lg transition-all duration-300 ${
                eventMode === mode
                  ? "font-semibold text-black"
                  : "text-gray-400"
              }`}
            >
              {t(
                mode === "single"
                  ? "Normal Event"
                  : mode === "group"
                    ? "Group Event"
                    : "Import Data",
              )}
              {eventMode === mode && (
                <span
                  className="absolute bottom-0 left-1/2 h-[3px] w-[60%] origin-center scale-x-0 bg-black transition-transform duration-300"
                  style={{ transform: "translateX(-50%) scaleX(1)" }}
                />
              )}
            </div>
          ))}
        </div>

        {eventMode === "single" ? (
          <>
            <EventForm
              title={title}
              setTitle={setTitle}
              amount={amount}
              setAmount={setAmount}
              dueDate={dueDate}
              setDueDate={setDueDate}
              category={category}
              setCategory={setCategory}
              type={type}
              setType={setType}
              status={status}
              setStatus={setStatus}
              isRecursive={isRecursive}
              setIsRecursive={setIsRecursive}
              recurrenceRange={recurrenceRange}
              setRecurrenceRange={setRecurrenceRange}
              recurrenceCount={recurrenceCount}
              setRecurrenceCount={setRecurrenceCount}
              types={types}
              handleAddType={handleAddType}
              notificationDate={notificationDate}
              setNotificationDate={setNotificationDate}
            />
          </>
        ) : eventMode === "group" ? (
          <>
            <GroupEventForm
              priority={priority}
              setPriority={setPriority}
              type_id={typeId}
              setTypeId={setTypeId}
              parentTitle={parentTitle}
              setParentTitle={setParentTitle}
              parentAmount={parentAmount}
              setParentAmount={setParentAmount}
              parentDescription={parentDescription}
              setParentDescription={setParentDescription}
              children={children}
              setChildren={setChildren}
              types={groupTypes[category]}
            />
          </>
        ) : eventMode === "import" ? (
          <DataImporter<EventDetails>
            templateFields={["title", "amount"]}
            onImport={handleImport}
            manualDropdowns={[
              { field: "category", options: ["income", "expense"] },
              { field: "type", options: [...new Set(types[category] || [])] },
              {
                field: "status",
                options: [
                  "completed",
                  "pending",
                  "cancelled",
                  "upcoming",
                  "overdue",
                ],
              },
              { field: "priority", options: ["low", "medium", "high"] },
              {
                field: "contact",
                options: [...new Set(contacts.map((c) => c.name))],
              },
              {
                field: "location",
                options: [
                  ...new Set(locationList.map((location) => location.name)),
                ],
              },
            ]}
            dateFields={["dueDate", "notificationDate"]}
          />
        ) : null}
        {eventMode === "import" && importedData.length > 0 && (
          <div className="mt-6 flex justify-end gap-4">
            <Button variant="outline" onClick={onClose}>
              {t("Cancel")}
            </Button>
            <Button
              className="bg-blue-500 text-white"
              onClick={handleSaveImportedData}
            >
              {t("Save Imported Data")}
            </Button>
          </div>
        )}
        {eventMode === "group" || eventMode === "single" ? (
          <>
            <div className="mt-4 flex items-center gap-4">
              <div className="flex-1">
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                  {t("Contact")}
                </label>
                <select
                  value={selectedContact?.email || ""}
                  onChange={(e) =>
                    setSelectedContact(
                      contactsData?.contacts.find(
                        (contact) => contact.email === e.target.value,
                      ) || null,
                    )
                  }
                  className="mt-1 block w-full rounded-lg border border-gray-300 bg-white px-3 py-2 dark:border-gray-600 dark:bg-gray-700 dark:text-gray-200"
                >
                  <option value="">{t("Select Contact")}</option>
                  {contactsData?.contacts.map((contact) => (
                    <option
                      key={contact.id || contact.email}
                      value={contact.email}
                    >
                      {contact.name}
                    </option>
                  ))}
                </select>
              </div>
              <button
                type="button"
                onClick={() => setSelectedContact(null)}
                className="mt-6 rounded-lg bg-red-500 px-3 py-2 text-sm font-medium text-white hover:bg-red-600"
              >
                {t("Remove")}
              </button>
            </div>

            <div className="mb-2 mt-4 flex items-center gap-4">
              <div className="flex-1">
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                  {t("Location")}
                </label>
                <select
                  value={selectedLocation?.name || ""}
                  onChange={(e) =>
                    setSelectedLocation(
                      locationList.find(
                        (location) => location.name === e.target.value,
                      ) || null,
                    )
                  }
                  className="mt-1 block w-full rounded-lg border border-gray-300 bg-white px-3 py-2 dark:border-gray-600 dark:bg-gray-700 dark:text-gray-200"
                >
                  <option value="">{t("Select Location")}</option>
                  {locationList.map((location) => (
                    <option key={location.id} value={location.name}>
                      {location.name}{" "}
                      {location.areWeOwners && (
                        <span className="font-semibold text-green-500">
                          {" "}
                          {"(Owners)"}
                        </span>
                      )}
                    </option>
                  ))}
                </select>
              </div>
              <button
                type="button"
                onClick={() => setSelectedLocation(null)}
                className="mt-6 rounded-lg bg-red-500 px-3 py-2 text-sm font-medium text-white hover:bg-red-600"
              >
                {t("Remove")}
              </button>
            </div>

            {/* Toggles Row */}
            <div className="mb-6 flex flex-col gap-4 md:flex-row">
              {selectedLocation?.areWeOwners && (
                <div className="flex flex-1 items-center gap-3">
                  <input
                    type="checkbox"
                    id="autoCreate"
                    checked={autoCreate}
                    onChange={() => setAutoCreate((prev) => !prev)}
                    className="form-checkbox h-5 w-5 text-blue-600"
                  />
                  <label
                    htmlFor="autoCreate"
                    className="text-sm text-gray-700 dark:text-gray-300"
                  >
                    {t("Auto-generate events?")}
                    <span className="block text-xs text-gray-500">
                      {t("Do you want to auto-generate")}{" "}
                      {category === "income" ? t("expenses") : t("incomes")}{" "}
                      {t("based on this location?")}
                    </span>
                  </label>
                </div>
              )}
              <div className="flex flex-1 items-center gap-3">
                <input
                  type="checkbox"
                  id="isTotalAmount"
                  checked={isTotalAmount}
                  onChange={() => setIsTotalAmount((prev) => !prev)}
                  className="form-checkbox h-5 w-5 text-blue-600"
                />
                <label
                  htmlFor="isTotalAmount"
                  className="text-sm text-gray-700 dark:text-gray-300"
                >
                  {t("Is this the Total Amount?")}
                  <span className="block text-xs text-gray-500">
                    {t(
                      "Is the amount you entered the total amount or just your cut from the location",
                    )}{" "}
                    {category === "income" ? t("expenses") : t("incomes")}{" "}
                    {t("based on this location?")}
                  </span>
                </label>
              </div>
            </div>
          </>
        ) : null}

        <div className="mt-6 flex justify-end gap-4">
          <Button variant="outline" onClick={onClose}>
            {t("Cancel")}
          </Button>
          <Button className="bg-blue-500 text-white" onClick={handleSave}>
            {t("Save Event")}
          </Button>
        </div>

        {showAddTypePopup && (
          <AddTypePopup
            onClose={() => setShowAddTypePopup(false)}
            onSave={handleSaveType}
          />
        )}
        {showSuccess && (
          <SuccessPopup
            message={successMessage || t("Event saved successfully!")}
            onClose={() => setShowSuccess(false)}
          />
        )}
        {showError && (
          <ErrorPopup
            message={
              errorMessage || t("An error occurred while saving the event.")
            }
            onClose={() => setShowError(false)}
          />
        )}
      </div>
    </div>
  );
};

export default AddEventPopup;
