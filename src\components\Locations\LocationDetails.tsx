import React, { useState, useMemo } from "react";
import {
  Phone,
  Mail,
  User,
  Calendar,
  Trash2,
  Edit,
  Building,
  DollarSign,
  Users,
  Calendar as CalendarIcon,
  Percent,
  History,
  PieChart,
  BarChart3,
  ChevronDown,
  ChevronUp,
  X,
  ChevronLeft,
  ChevronRight,
  Clock,
  CheckCircle,
} from "lucide-react";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from "@/components/cards/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Location } from "@/lib/types/location";
import useLanguage from "@/hooks/useLanguage";
import DeleteConfirmationModal from "../Modals/DeleteConfirmationModal";
import { useRouter } from "next/navigation";
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  Cell,
} from "recharts";
import { useLocationServices } from "@/hooks/useLocations";
import { toast } from "react-hot-toast";
import { FinancialHistory } from "./FinancialHistory";

interface LocationDetailsProps {
  location?: Location;
  onEdit: () => void;
  onDelete: () => void;
  onClose?: () => void;
}

const LocationDetails: React.FC<LocationDetailsProps> = ({
  location,
  onEdit,
  onDelete,
  onClose,
}) => {
  const { t, language } = useLanguage();
  const router = useRouter();
  const locationServices = useLocationServices();

  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [selectedMonth, setSelectedMonth] = useState<string | null>(null);
  const [isDailyViewOpen, setIsDailyViewOpen] = useState<boolean>(false);
  const [isDeleting, setIsDeleting] = useState(false);
  const [deleteError, setDeleteError] = useState<string | null>(null);

  const getStatusColor = (status: Location["status"]) =>
    status === "active"
      ? "bg-green-100 text-green-800"
      : "bg-red-100 text-red-800";

  const colors = {
    office: "bg-blue-100 text-blue-800",
    warehouse: "bg-yellow-100 text-yellow-800",
    retail: "bg-green-100 text-green-800",
    other: "bg-gray-100 text-gray-800",
  };

  const getTypeColor = (type: keyof typeof colors) => {
    return colors[type];
  };

  // Calculate capacity usage statistics
  const activeReservations =
    location?.reservations?.filter((r) => {
      // Check if reservation is active or pending
      const isActiveStatus = r.status === "active" || r.status === "pending";

      // Check if the end date is in the future
      const endDate = new Date(r.endDate);
      const isNotExpired = endDate >= new Date();

      // Only include reservations that are both active/pending AND not expired
      return isActiveStatus && isNotExpired;
    }) || [];

  const usedCapacity = activeReservations.reduce(
    (sum, res) => sum + res.capacity,
    0,
  );
  const availableCapacity = location ? location.capacity - usedCapacity : 0;
  const capacityUtilization = location
    ? (usedCapacity / location.capacity) * 100
    : 0;

  // Process monthly capacity data
  const capacityData = useMemo(() => {
    if (!location) return [];

    const now = new Date();
    const data = [];

    // Generate past 6 months and future 6 months
    for (let i = -6; i <= 6; i++) {
      const date = new Date(now.getFullYear(), now.getMonth() + i, 1);
      const monthKey = date.toISOString().substring(0, 7); // YYYY-MM format
      const monthName = date.toLocaleString(
        language === "ar" ? "ar-EG" : "en-US",
        { month: "short" },
      );
      const year = date.getFullYear();

      // Calculate reserved capacity for this month
      let reservedCapacity = 0;
      if (location.reservations) {
        reservedCapacity = location.reservations
          .filter((res) => {
            const startDate = new Date(res.startDate);
            const endDate = new Date(res.endDate);

            // Check if reservation overlaps with this month
            const monthStart = new Date(date.getFullYear(), date.getMonth(), 1);
            const monthEnd = new Date(
              date.getFullYear(),
              date.getMonth() + 1,
              0,
            );

            return (
              startDate <= monthEnd &&
              endDate >= monthStart &&
              (res.status === "active" || res.status === "pending")
            );
          })
          .reduce((sum, res) => sum + res.capacity, 0);
      }

      const isPast = date < now;

      data.push({
        month: monthName,
        year,
        monthKey,
        totalCapacity: location.capacity,
        reservedCapacity,
        availableCapacity: location.capacity - reservedCapacity,
        isPast,
      });
    }

    return data;
  }, [location]);

  // Process daily capacity data for selected month
  const dailyCapacityData = useMemo(() => {
    if (!location || !selectedMonth) return [];

    const [year, month] = selectedMonth.split("-").map(Number);
    const daysInMonth = new Date(year, month, 0).getDate();
    const data = [];

    for (let day = 1; day <= daysInMonth; day++) {
      const date = new Date(year, month - 1, day);
      const dateStr = date.toISOString().split("T")[0]; // YYYY-MM-DD

      // Calculate reserved capacity for this day
      let reservedCapacity = 0;
      const dayReservations: any[] = [];

      if (location.reservations) {
        location.reservations
          .filter((res) => {
            const startDate = new Date(res.startDate);
            const endDate = new Date(res.endDate);

            // Check if reservation includes this day
            return (
              startDate <= date &&
              endDate >= date &&
              (res.status === "active" || res.status === "pending")
            );
          })
          .forEach((res) => {
            reservedCapacity += res.capacity;
            dayReservations.push({
              id: res.id,
              clientName: res.clientName,
              capacity: res.capacity,
            });
          });
      }

      data.push({
        day,
        date: dateStr,
        weekday: date.toLocaleString("default", { weekday: "short" }),
        totalCapacity: location.capacity,
        reservedCapacity,
        availableCapacity: location.capacity - reservedCapacity,
        reservations: dayReservations,
      });
    }

    return data;
  }, [location, selectedMonth]);

  const handleMonthClick = (monthKey: string) => {
    if (selectedMonth === monthKey) {
      setIsDailyViewOpen(!isDailyViewOpen);
    } else {
      setSelectedMonth(monthKey);
      setIsDailyViewOpen(true);
    }
  };

  const handleCloseMonthView = () => {
    setSelectedMonth(null);
    setIsDailyViewOpen(false);
  };

  const handleDelete = async () => {
    if (location && location.id) {
      setIsDeleting(true);
      setDeleteError(null);

      try {
        await locationServices.deleteLocation(location.id);
        // Toast is now handled in the service, so we don't need to show it here
        onDelete(); // Update UI after successful deletion
        setIsDeleteModalOpen(false); // Close modal
      } catch (error: any) {
        console.error("Error deleting location:", error);
        // Handle error response for the modal display
        if (error.response && error.response.data) {
          const errorMessage =
            error.response.data.details ||
            error.response.data.error ||
            error.response.data.warning ||
            error.response.data.message ||
            "Error deleting location";
          setDeleteError(errorMessage);
        } else {
          setDeleteError("Failed to delete location. Please try again.");
        }
      } finally {
        setIsDeleting(false);
      }
    }
  };

  if (!location) {
    return (
      <Card className="h-full w-full">
        <CardContent className="flex flex-col items-center justify-center py-16">
          <Building className="h-16 w-16 text-gray-300 dark:text-gray-600" />
          <p className="mt-4 max-w-sm text-center text-gray-500 dark:text-gray-400">
            {t("noLocationSelected")}
          </p>
        </CardContent>
      </Card>
    );
  }

  return (
    <>
      <Card className="w-full">
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-4">
          <div className="flex items-center gap-3">
            {onClose && (
              <Button variant="ghost" size="sm" onClick={onClose}>
                <X className="h-4 w-4" />
              </Button>
            )}
            <div>
              <CardTitle className="text-xl font-semibold">
                {location?.name}
              </CardTitle>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                {location?.address}
              </p>
            </div>
          </div>
          <div className="flex items-center gap-2">
            {/* <Button
              variant="outline"
              size="sm"
              onClick={() => {
                router.push(`/dashboard/locations/${location?.id}/history`);
              }}
              className="flex items-center gap-2"
            >
              <History className="h-4 w-4" />
              {t("viewFullHistory")}
            </Button> */}
            <Button
              variant="outline"
              size="sm"
              onClick={onEdit}
              className="flex items-center gap-2 border-blue-200 bg-blue-50 text-blue-700 hover:bg-blue-100 hover:text-blue-800 dark:border-blue-800 dark:bg-blue-900/20 dark:text-blue-400 dark:hover:bg-blue-900/30"
            >
              <Edit className="h-4 w-4" />
              {t("edit")}
            </Button>
            <Button
              variant="destructive"
              size="sm"
              onClick={() => setIsDeleteModalOpen(true)}
              className="flex items-center gap-2 bg-red-600 text-white hover:bg-red-700 dark:bg-red-700 dark:hover:bg-red-800"
            >
              <Trash2 className="h-4 w-4" />
              {t("delete")}
            </Button>
          </div>
        </CardHeader>

        <CardContent className="space-y-8 pt-6">
          {/* Main Info Section */}
          <div className="rounded-lg bg-gray-50 p-4 dark:bg-gray-800/50">
            <div className="flex items-start gap-4">
              <Building className="mt-1 h-6 w-6 text-gray-500 dark:text-gray-400" />
              <div className="flex-1">
                <h3 className="text-xl font-medium text-gray-900 dark:text-gray-100">
                  {location.name}
                </h3>
                <p className="mt-1 text-gray-600 dark:text-gray-300">
                  {location.address}
                </p>
                <div className="mt-3 flex items-center gap-2">
                  <Badge
                    variant="secondary"
                    className={`px-3 py-1 ${getTypeColor(location.type as keyof typeof colors)}`}
                  >
                    {t(location.type)}
                  </Badge>
                  <Badge
                    variant="secondary"
                    className={`px-3 py-1 ${getStatusColor(location.status)}`}
                  >
                    {t(location.status)}
                  </Badge>
                  <Badge
                    variant="secondary"
                    className="bg-purple-100 text-purple-800 dark:bg-purple-900/20 dark:text-purple-400"
                  >
                    {t("capacity")}:{" "}
                    {Number(location.capacity).toLocaleString(
                      language === "ar" ? "ar-EG" : "en-US",
                    )}
                  </Badge>
                </div>
              </div>
            </div>
          </div>

          {/* Monthly Capacity Chart - New Section */}
          <div className="space-y-4">
            <div className="mb-4 flex items-center justify-between">
              <h4 className="flex items-center gap-2 text-sm font-medium text-gray-900 dark:text-white">
                <BarChart3 className="h-4 w-4 text-gray-500" />
                {t("capacityTrend")}
              </h4>

              {/* Add close button for selected month */}
              {selectedMonth && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={handleCloseMonthView}
                  className="text-gray-500 hover:text-gray-700"
                >
                  <X className="mr-1.5 h-4 w-4" />
                  {t("clearSelection")}
                </Button>
              )}
            </div>

            <div className="rounded-lg bg-white p-6 shadow-sm dark:bg-gray-800">
              <div className="h-80">
                <ResponsiveContainer width="100%" height="100%">
                  <BarChart
                    data={capacityData}
                    margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
                    barGap={0}
                    barSize={20}
                    onClick={(data) =>
                      data &&
                      handleMonthClick(
                        data.activePayload?.[0]?.payload.monthKey,
                      )
                    }
                  >
                    <CartesianGrid strokeDasharray="3 3" vertical={false} />
                    <XAxis
                      dataKey="month"
                      axisLine={false}
                      tickLine={false}
                      tickFormatter={(value, index) => {
                        const item = capacityData[index];
                        return `${value} ${item.year !== new Date().getFullYear() ? item.year.toString().substr(2, 2) : ""}`;
                      }}
                    />
                    <YAxis axisLine={false} tickLine={false} />
                    <Tooltip
                      formatter={(value, name) => {
                        const formattedValue = Number(value).toLocaleString(
                          language === "ar" ? "ar-EG" : "en-US",
                        );
                        if (name === "reservedCapacity")
                          return [formattedValue, t("reservedCapacity")];
                        if (name === "availableCapacity")
                          return [formattedValue, t("availableCapacity")];
                        return [formattedValue, name];
                      }}
                      labelFormatter={(label, payload) => {
                        if (payload && payload.length > 0) {
                          const { year, month } = payload[0].payload;
                          return `${month} ${Number(year).toLocaleString(
                            language === "ar" ? "ar-EG" : "en-US",
                          )}`;
                        }
                        return label;
                      }}
                    />
                    <Legend
                      formatter={(value) => {
                        if (value === "reservedCapacity")
                          return t("reservedCapacity");
                        if (value === "availableCapacity")
                          return t("availableCapacity");
                        return value;
                      }}
                    />
                    <Bar
                      dataKey="reservedCapacity"
                      name="reservedCapacity"
                      stackId="a"
                      fill="#3b82f6"
                    >
                      {capacityData.map((entry, index) => (
                        <Cell
                          key={`cell-${index}`}
                          fill={
                            entry.monthKey === selectedMonth
                              ? "#2563eb"
                              : "#3b82f6"
                          }
                          cursor="pointer"
                        />
                      ))}
                    </Bar>
                    <Bar
                      dataKey="availableCapacity"
                      name="availableCapacity"
                      stackId="a"
                      fill="#e2e8f0"
                    >
                      {capacityData.map((entry, index) => (
                        <Cell
                          key={`cell-${index}`}
                          fill={
                            entry.monthKey === selectedMonth
                              ? "#cbd5e1"
                              : "#e2e8f0"
                          }
                          cursor="pointer"
                        />
                      ))}
                    </Bar>
                  </BarChart>
                </ResponsiveContainer>
              </div>

              <div className="mt-2 text-center text-sm text-gray-500 dark:text-gray-400">
                {selectedMonth
                  ? t("clickClearSelectionToResetView")
                  : t("clickOnMonthToViewDailyCapacity")}
              </div>
            </div>

            {/* Daily Capacity View */}
            {selectedMonth && isDailyViewOpen && (
              <div className="rounded-lg bg-white p-6 shadow-sm dark:bg-gray-800">
                <div className="mb-6 flex items-center justify-between">
                  <h5 className="text-md font-semibold text-gray-900 dark:text-white">
                    {new Date(selectedMonth + "-01").toLocaleString(
                      language === "ar" ? "ar-EG" : "en-US",
                      {
                        month: "long",
                        year: "numeric",
                      },
                    )}{" "}
                    {t("dailyCapacity")}
                  </h5>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setIsDailyViewOpen(false)}
                  >
                    <X className="h-4 w-4" />
                  </Button>
                </div>

                <div className="grid grid-cols-7 gap-2">
                  {[
                    t("sun") || "Sun",
                    t("mon") || "Mon",
                    t("tue") || "Tue",
                    t("wed") || "Wed",
                    t("thu") || "Thu",
                    t("fri") || "Fri",
                    t("sat") || "Sat",
                  ].map((day) => (
                    <div
                      key={day}
                      className="p-1 text-center text-xs font-medium text-gray-500 dark:text-gray-400"
                    >
                      {day}
                    </div>
                  ))}

                  {/* Calculate empty cells at the start of the month */}
                  {Array.from({
                    length: new Date(selectedMonth + "-01").getDay(),
                  }).map((_, i) => (
                    <div
                      key={`empty-start-${i}`}
                      className="h-24 rounded-lg bg-gray-50 dark:bg-gray-800/50"
                    ></div>
                  ))}

                  {dailyCapacityData.map((dayData) => {
                    const utilization =
                      (dayData.reservedCapacity / dayData.totalCapacity) * 100;

                    return (
                      <div
                        key={dayData.date}
                        className={`h-24 rounded-lg border p-2 ${
                          dayData.date ===
                          new Date().toISOString().split("T")[0]
                            ? "border-blue-500 dark:border-blue-400"
                            : "border-gray-200 dark:border-gray-700"
                        } cursor-pointer transition-shadow hover:shadow-md`}
                      >
                        <div className="flex h-full flex-col">
                          <div className="flex items-center justify-between">
                            <span className="font-medium">
                              {Number(dayData.day).toLocaleString(
                                language === "ar" ? "ar-EG" : "en-US",
                              )}
                            </span>
                            {dayData.reservedCapacity > 0 && (
                              <span
                                className={`rounded-full px-1.5 py-0.5 text-xs ${
                                  utilization > 80
                                    ? "bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400"
                                    : utilization > 50
                                      ? "bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400"
                                      : "bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400"
                                }`}
                              >
                                {Number(utilization).toLocaleString(
                                  language === "ar" ? "ar-EG" : "en-US",
                                  {
                                    minimumFractionDigits: 0,
                                    maximumFractionDigits: 0,
                                  },
                                )}
                                %
                              </span>
                            )}
                          </div>

                          <div className="mt-auto">
                            {dayData.reservations
                              .slice(0, 2)
                              .map((res, idx) => (
                                <div
                                  key={res.id}
                                  className="truncate text-xs text-gray-600 dark:text-gray-400"
                                >
                                  {res.clientName.substring(0, 8)}...
                                </div>
                              ))}
                            {dayData.reservations.length > 2 && (
                              <div className="text-xs text-gray-500 dark:text-gray-500">
                                +
                                {Number(
                                  dayData.reservations.length - 2,
                                ).toLocaleString(
                                  language === "ar" ? "ar-EG" : "en-US",
                                )}{" "}
                                {t("more")}
                              </div>
                            )}
                          </div>
                        </div>
                      </div>
                    );
                  })}

                  {/* Calculate empty cells at the end of the month */}
                  {Array.from({
                    length:
                      (7 -
                        ((dailyCapacityData.length +
                          new Date(selectedMonth + "-01").getDay()) %
                          7)) %
                      7,
                  }).map((_, i) => (
                    <div
                      key={`empty-end-${i}`}
                      className="h-24 rounded-lg bg-gray-50 dark:bg-gray-800/50"
                    ></div>
                  ))}
                </div>

                {dailyCapacityData.some(
                  (day) => day.reservations.length > 0,
                ) && (
                  <div className="mt-6 space-y-4">
                    <h6 className="font-medium text-gray-900 dark:text-white">
                      {t("monthReservations")}
                    </h6>
                    <div className="space-y-2">
                      {dailyCapacityData
                        .filter((day) => day.reservations.length > 0)
                        .map((day) => (
                          <div
                            key={day.date}
                            className="rounded-lg bg-gray-50 p-3 dark:bg-gray-800/50"
                          >
                            <div className="mb-2 flex items-center justify-between">
                              <div className="font-medium text-gray-900 dark:text-white">
                                {new Date(day.date).toLocaleDateString(
                                  language === "ar" ? "ar-EG" : "en-US",
                                )}
                              </div>
                              <Badge>
                                {Number(day.reservedCapacity).toLocaleString(
                                  language === "ar" ? "ar-EG" : "en-US",
                                )}
                                /
                                {Number(day.totalCapacity).toLocaleString(
                                  language === "ar" ? "ar-EG" : "en-US",
                                )}{" "}
                                {t("units")}
                              </Badge>
                            </div>
                            <div className="space-y-1.5">
                              {day.reservations.map((res) => (
                                <div
                                  key={res.id}
                                  className="flex justify-between text-sm"
                                >
                                  <span className="text-gray-700 dark:text-gray-300">
                                    {res.clientName}
                                  </span>
                                  <span className="text-gray-600 dark:text-gray-400">
                                    {Number(res.capacity).toLocaleString(
                                      language === "ar" ? "ar-EG" : "en-US",
                                    )}{" "}
                                    {t("units")}
                                  </span>
                                </div>
                              ))}
                            </div>
                          </div>
                        ))}
                    </div>
                  </div>
                )}
              </div>
            )}
          </div>

          {/* New Capacity Allocation Section */}
          <div className="space-y-4">
            <h4 className="mb-4 text-sm font-medium text-gray-900 dark:text-white">
              {t("capacityAllocation")}
            </h4>

            <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
              {/* Capacity Overview Card */}
              <div className="rounded-lg bg-white p-6 shadow-sm dark:bg-gray-800">
                <div className="mb-4 flex items-center justify-between">
                  <div>
                    <h5 className="text-sm font-medium text-gray-700 dark:text-gray-300">
                      {t("totalCapacity")}
                    </h5>
                    <p className="mt-1 text-2xl font-bold text-gray-900 dark:text-gray-100">
                      {Number(location?.capacity || 0).toLocaleString(
                        language === "ar" ? "ar-EG" : "en-US",
                      )}
                    </p>
                  </div>
                  <PieChart className="h-8 w-8 text-blue-500 opacity-70 dark:text-blue-400" />
                </div>

                <div className="space-y-2">
                  {/* Capacity utilization bar */}
                  <div className="mt-2">
                    <div className="mb-1 flex justify-between text-sm">
                      <span className="text-gray-600 dark:text-gray-400">
                        {t("capacityUtilization")}
                      </span>
                      <span className="font-medium">
                        {Number(capacityUtilization).toLocaleString(
                          language === "ar" ? "ar-EG" : "en-US",
                          {
                            minimumFractionDigits: 0,
                            maximumFractionDigits: 0,
                          },
                        )}
                        %
                      </span>
                    </div>
                    <div className="h-3 overflow-hidden rounded-full bg-gray-200 dark:bg-gray-700">
                      <div
                        className="h-full rounded-full bg-blue-500 dark:bg-blue-600"
                        style={{ width: `${capacityUtilization}%` }}
                      ></div>
                    </div>
                  </div>

                  {/* Capacity breakdown */}
                  <div className="mt-4 flex justify-between text-sm">
                    <div>
                      <span className="text-gray-500 dark:text-gray-400">
                        {t("usedCapacity")}
                      </span>
                      <p className="font-medium text-gray-900 dark:text-gray-100">
                        {Number(usedCapacity).toLocaleString(
                          language === "ar" ? "ar-EG" : "en-US",
                        )}
                      </p>
                    </div>
                    <div>
                      <span className="text-gray-500 dark:text-gray-400">
                        {t("availableCapacity")}
                      </span>
                      <p className="font-medium text-gray-900 dark:text-gray-100">
                        {Number(availableCapacity).toLocaleString(
                          language === "ar" ? "ar-EG" : "en-US",
                        )}
                      </p>
                    </div>
                    <div>
                      <span className="text-gray-500 dark:text-gray-400">
                        {t("activeReservations")}
                      </span>
                      <p className="font-medium text-gray-900 dark:text-gray-100">
                        {Number(activeReservations.length).toLocaleString(
                          language === "ar" ? "ar-EG" : "en-US",
                        )}
                      </p>
                    </div>
                  </div>
                </div>
              </div>

              {/* Current Reservations Card */}
              <div className="rounded-lg bg-white p-6 shadow-sm dark:bg-gray-800">
                <h5 className="mb-4 text-sm font-medium text-gray-700 dark:text-gray-300">
                  {t("currentReservations")}
                </h5>

                {activeReservations.length > 0 ? (
                  <div className="space-y-3">
                    {/* Capacity segments visualization */}
                    <div className="flex h-6 overflow-hidden rounded-full bg-gray-200 dark:bg-gray-700">
                      {activeReservations.map((res, idx) => {
                        const percentWidth =
                          (res.capacity / location!.capacity) * 100;
                        const colors = [
                          "bg-blue-500 dark:bg-blue-600",
                          "bg-green-500 dark:bg-green-600",
                          "bg-purple-500 dark:bg-purple-600",
                          "bg-yellow-500 dark:bg-yellow-600",
                          "bg-pink-500 dark:bg-pink-600",
                        ];
                        const color = colors[idx % colors.length];

                        return (
                          <div
                            key={res.id}
                            className={`${color} h-full`}
                            style={{ width: `${percentWidth}%` }}
                            title={`${res.clientName}: ${Number(
                              res.capacity,
                            ).toLocaleString(
                              language === "ar" ? "ar-EG" : "en-US",
                            )} units (${Number(percentWidth).toLocaleString(
                              language === "ar" ? "ar-EG" : "en-US",
                              {
                                minimumFractionDigits: 1,
                                maximumFractionDigits: 1,
                              },
                            )}%)`}
                          />
                        );
                      })}
                      {availableCapacity > 0 && (
                        <div
                          className="h-full bg-gray-300 dark:bg-gray-600"
                          style={{
                            width: `${(availableCapacity / location!.capacity) * 100}%`,
                          }}
                          title={`Available: ${Number(
                            availableCapacity,
                          ).toLocaleString(
                            language === "ar" ? "ar-EG" : "en-US",
                          )} units`}
                        />
                      )}
                    </div>

                    {/* Individual reservations list */}
                    <div className="mt-4 space-y-2">
                      {activeReservations.map((res, idx) => (
                        <div
                          key={res.id}
                          className="flex items-center justify-between rounded-md bg-gray-50 p-2 dark:bg-gray-700/50"
                        >
                          <div className="flex items-center space-x-2">
                            <div
                              className={`h-3 w-3 rounded-full 
                            ${idx % 2 === 0 ? "bg-blue-500" : "bg-green-500"}`}
                            />
                            <div>
                              <p className="text-sm font-medium text-gray-900 dark:text-gray-100">
                                {res.clientName}
                              </p>
                              <p className="text-xs text-gray-500">
                                {new Date(res.startDate).toLocaleDateString(
                                  language === "ar" ? "ar-EG" : "en-US",
                                )}{" "}
                                -{" "}
                                {new Date(res.endDate).toLocaleDateString(
                                  language === "ar" ? "ar-EG" : "en-US",
                                )}
                              </p>
                            </div>
                          </div>
                          <div className="flex flex-col items-end">
                            <span className="text-sm font-medium text-gray-900 dark:text-gray-100">
                              {Number(res.capacity).toLocaleString(
                                language === "ar" ? "ar-EG" : "en-US",
                              )}{" "}
                              {t("units")}
                            </span>
                            <span className="text-xs text-gray-500">
                              {language === "ar" ? "%" : ""}
                              {Number(
                                (res.capacity / location!.capacity) * 100,
                              ).toLocaleString(
                                language === "ar" ? "ar-EG" : "en-US",
                                {
                                  minimumFractionDigits: 0,
                                  maximumFractionDigits: 0,
                                },
                              )}
                              {language === "ar" ? "" : "%"}
                            </span>
                          </div>
                        </div>
                      ))}

                      {/* Available capacity row */}
                      {availableCapacity > 0 && (
                        <div className="flex items-center justify-between rounded-md border border-dashed border-gray-300 bg-gray-50 p-2 dark:border-gray-600 dark:bg-gray-700/50">
                          <div className="flex items-center space-x-2">
                            <div className="h-3 w-3 rounded-full bg-gray-400" />
                            <p className="text-sm font-medium text-gray-500 dark:text-gray-400">
                              {t("available")}
                            </p>
                          </div>
                          <div className="flex flex-col items-end">
                            <span className="text-sm font-medium text-gray-900 dark:text-gray-100">
                              {Number(availableCapacity).toLocaleString(
                                language === "ar" ? "ar-EG" : "en-US",
                              )}{" "}
                              {t("units")}
                            </span>
                            <span className="text-xs text-gray-500">
                              {language === "ar" ? "%" : ""}
                              {Number(
                                (availableCapacity / location!.capacity) * 100,
                              ).toLocaleString(
                                language === "ar" ? "ar-EG" : "en-US",
                                {
                                  minimumFractionDigits: 0,
                                  maximumFractionDigits: 0,
                                },
                              )}
                              {language === "ar" ? "" : "%"}
                            </span>
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                ) : (
                  <div className="flex h-32 items-center justify-center text-center">
                    <div className="text-gray-500 dark:text-gray-400">
                      <Calendar className="mx-auto mb-2 h-8 w-8 opacity-40" />
                      <p>{t("noActiveReservations")}</p>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>

          <div className="space-y-4">
            <h4 className="mb-4 text-sm font-medium text-gray-900 dark:text-white">
              {t("ownershipDetails")}
            </h4>
            <div className="grid grid-cols-1 gap-6 md:grid-cols-3">
              <div className="flex items-start gap-3">
                <Building className="mt-1 h-5 w-5 text-gray-500 dark:text-gray-400" />
                <div></div>
                <p className="text-sm font-medium text-gray-500 dark:text-gray-400">
                  {t("ownedBy")}
                </p>
                <p className="mt-1 text-gray-900 dark:text-gray-100">
                  {/* Display the name from primaryOwner object if available, fallback to ownedBy */}
                  {location.primaryOwner?.name ||
                    location.ownedBy ||
                    t("notSpecified")}
                  {location.primaryOwner?.percentage &&
                    ` (${language === "ar" ? "%" : ""}${Number(
                      location.primaryOwner.percentage,
                    ).toLocaleString(language === "ar" ? "ar-EG" : "en-US", {
                      minimumFractionDigits: 0,
                      maximumFractionDigits: 0,
                    })}${language === "ar" ? "" : "%"})`}
                </p>
              </div>
            </div>

            <div className="flex items-start gap-3">
              <Percent className="mt-1 h-5 w-5 text-gray-500 dark:text-gray-400" />
              <div>
                <p className="text-sm font-medium text-gray-500 dark:text-gray-400">
                  {t("ourPercentage")}
                </p>
                <p className="mt-1 text-gray-900 dark:text-gray-100">
                  {language === "ar" ? "%" : ""}
                  {Number(location.ourPercentage).toLocaleString(
                    language === "ar" ? "ar-EG" : "en-US",
                    {
                      minimumFractionDigits: 0,
                      maximumFractionDigits: 0,
                    },
                  )}
                  {language === "ar" ? "" : "%"}
                </p>
              </div>
            </div>

            <div className="flex items-start gap-3">
              <Users className="mt-1 h-5 w-5 text-gray-500 dark:text-gray-400" />
              <div>
                {location.ownershipShares
                  ? location.ownershipShares.map((share, idx) => (
                      <p
                        key={idx}
                        className="pl-2 text-sm text-gray-600 dark:text-gray-400"
                      >
                        • {share.name}{" "}
                        <span className="font-medium">
                          ({language === "ar" ? "%" : ""}
                          {Number(share.percentage).toLocaleString(
                            language === "ar" ? "ar-EG" : "en-US",
                            {
                              minimumFractionDigits: 0,
                              maximumFractionDigits: 0,
                            },
                          )}
                          {language === "ar" ? "" : "%"})
                        </span>
                      </p>
                    ))
                  : location.sharedWith.map((partner, idx) => (
                      <p
                        key={idx}
                        className="pl-2 text-sm text-gray-600 dark:text-gray-400"
                      >
                        • {partner}
                      </p>
                    ))}
              </div>
            </div>
          </div>

          {/* Reservation Contacts - Enhanced UI */}
          {activeReservations.length > 0 && (
            <div className="rounded-lg border border-gray-200 bg-white p-6 shadow-sm dark:border-gray-700 dark:bg-gray-800">
              <div className="mb-4 flex items-center gap-3">
                <div className="flex h-10 w-10 items-center justify-center rounded-lg bg-blue-100 dark:bg-blue-900/20">
                  <Users className="h-5 w-5 text-blue-600 dark:text-blue-400" />
                </div>
                <div>
                  <h5 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
                    {t("reservationContacts")}
                  </h5>
                  <p className="text-sm text-gray-500 dark:text-gray-400">
                    {activeReservations.length} {t("activeReservations")}
                  </p>
                </div>
              </div>

              <div className="space-y-3">
                {activeReservations.map((reservation, index) => (
                  <div
                    key={reservation.id}
                    className="group relative rounded-lg border border-gray-100 bg-gray-50 p-4 transition-all hover:border-blue-200 hover:bg-blue-50/50 hover:shadow-sm dark:border-gray-700 dark:bg-gray-700/30 dark:hover:border-blue-700 dark:hover:bg-blue-900/10"
                  >
                    {/* Reservation Color Indicator */}
                    <div
                      className={`absolute left-0 top-0 h-full w-1 rounded-l-lg ${
                        index % 3 === 0
                          ? "bg-blue-500"
                          : index % 3 === 1
                            ? "bg-green-500"
                            : "bg-purple-500"
                      }`}
                    />

                    <div className="ml-4">
                      {/* Header with Client Name and Capacity */}
                      <div className="mb-3 flex items-start justify-between">
                        <div className="flex items-center gap-3">
                          <div
                            className={`flex h-8 w-8 items-center justify-center rounded-full text-xs font-medium text-white ${
                              index % 3 === 0
                                ? "bg-blue-500"
                                : index % 3 === 1
                                  ? "bg-green-500"
                                  : "bg-purple-500"
                            }`}
                          >
                            {reservation.clientName.charAt(0).toUpperCase()}
                          </div>
                          <div>
                            <p className="text-base font-semibold text-gray-900 dark:text-gray-100">
                              {reservation.clientName}
                            </p>
                            <p className="text-xs text-gray-500 dark:text-gray-400">
                              {t("client")}
                            </p>
                          </div>
                        </div>

                        <div className="flex flex-col items-end gap-2">
                          <Badge
                            variant="outline"
                            className={`border-none font-medium ${
                              index % 3 === 0
                                ? "bg-blue-100 text-blue-700 dark:bg-blue-900/30 dark:text-blue-400"
                                : index % 3 === 1
                                  ? "bg-green-100 text-green-700 dark:bg-green-900/30 dark:text-green-400"
                                  : "bg-purple-100 text-purple-700 dark:bg-purple-900/30 dark:text-purple-400"
                            }`}
                          >
                            {Number(reservation.capacity).toLocaleString(
                              language === "ar" ? "ar-EG" : "en-US",
                            )}{" "}
                            {t("units")}
                          </Badge>
                          <span className="text-xs text-gray-500">
                            {language === "ar" ? "%" : ""}
                            {Number(
                              (reservation.capacity / location!.capacity) * 100,
                            ).toLocaleString(
                              language === "ar" ? "ar-EG" : "en-US",
                              {
                                minimumFractionDigits: 1,
                                maximumFractionDigits: 1,
                              },
                            )}
                            {language === "ar" ? "" : "%"}
                          </span>
                        </div>
                      </div>

                      {/* Reservation Details Grid */}
                      <div className="grid grid-cols-1 gap-3 sm:grid-cols-2 lg:grid-cols-3">
                        {/* Duration */}
                        <div className="flex items-center gap-2 rounded-md bg-white p-3 dark:bg-gray-800/50">
                          <Calendar className="h-4 w-4 text-gray-400" />
                          <div>
                            <p className="text-xs font-medium text-gray-500 dark:text-gray-400">
                              {t("duration")}
                            </p>
                            <p className="text-sm font-medium text-gray-900 dark:text-gray-100">
                              {new Date(
                                reservation.startDate,
                              ).toLocaleDateString(
                                language === "ar" ? "ar-EG" : "en-US",
                              )}{" "}
                              -{" "}
                              {new Date(reservation.endDate).toLocaleDateString(
                                language === "ar" ? "ar-EG" : "en-US",
                              )}
                            </p>
                          </div>
                        </div>

                        {/* Days Remaining */}
                        <div className="flex items-center gap-2 rounded-md bg-white p-3 dark:bg-gray-800/50">
                          <Clock className="h-4 w-4 text-gray-400" />
                          <div>
                            <p className="text-xs font-medium text-gray-500 dark:text-gray-400">
                              {t("daysRemaining")}
                            </p>
                            <p className="text-sm font-medium text-gray-900 dark:text-gray-100">
                              {Number(
                                Math.max(
                                  0,
                                  Math.ceil(
                                    (new Date(reservation.endDate).getTime() -
                                      new Date().getTime()) /
                                      (1000 * 60 * 60 * 24),
                                  ),
                                ),
                              ).toLocaleString(
                                language === "ar" ? "ar-EG" : "en-US",
                              )}{" "}
                              {t("days")}
                            </p>
                          </div>
                        </div>

                        {/* Reservation Status */}
                        <div className="flex items-center gap-2 rounded-md bg-white p-3 dark:bg-gray-800/50">
                          <CheckCircle className="h-4 w-4 text-green-500" />
                          <div>
                            <p className="text-xs font-medium text-gray-500 dark:text-gray-400">
                              {t("status")}
                            </p>
                            <p className="text-sm font-medium text-green-600 dark:text-green-400">
                              {t("active")}
                            </p>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Footer Information */}
          <div className="mt-6 border-t pt-4 dark:border-gray-700"></div>
          <div className="flex items-start gap-3">
            <Calendar className="mt-1 h-5 w-5 text-gray-500 dark:text-gray-400" />
            <div>
              <p className="text-sm font-medium text-gray-500 dark:text-gray-400">
                {t("created")}
              </p>
              <p className="mt-1 text-gray-900 dark:text-gray-100">
                {new Date(location.createdAt).toLocaleDateString(
                  language === "ar" ? "ar-EG" : "en-US",
                )}{" "}
                {t("by")} {location.createdBy}
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Always show Financial History */}
      {location && (
        <div className="mt-6">
          <FinancialHistory locationId={location.id} isLoading={false} />
        </div>
      )}

      <DeleteConfirmationModal
        isOpen={isDeleteModalOpen}
        onClose={() => {
          setIsDeleteModalOpen(false);
          setDeleteError(null);
        }}
        onConfirm={handleDelete}
        message={`${t("deleteLocationWarning")} ${t("deleteLocationConditions")}`}
        title={t("deleteLocation")}
        itemName={location?.name || ""}
        isLoading={isDeleting}
        error={deleteError}
      />
    </>
  );
};

export default LocationDetails;
