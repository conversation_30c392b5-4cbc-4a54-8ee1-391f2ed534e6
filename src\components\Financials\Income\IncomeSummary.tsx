import React, { useEffect, useState, useMemo } from "react";
import { EventDetails } from "@/lib/interfaces/finaces";
import { BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, PieChart, Pie, Cell, Legend, LineChart, Line } from "recharts";
import { FaChevronDown, FaChevronUp } from "react-icons/fa";
import DatePicker from "react-datepicker";
import "react-datepicker/dist/react-datepicker.css";
import useLanguage from "@/hooks/useLanguage";
import { useSpring, animated } from '@react-spring/web';
import {
    formatCurrency,
    formatNumber,
    processCategoryData,
    getChartConfig,
    filterValidEvents,
    CHART_COLORS
} from "@/utils/analyticsUtils";

interface IncomeSummaryProps {
    incomes: EventDetails[];
}

const IncomeSummary: React.FC<IncomeSummaryProps> = ({ incomes }) => {
    console.log("Incomes in IncomeSummary:", incomes);
    const { t, language } = useLanguage();
    const [selectedStartDate, setSelectedStartDate] = useState<Date | null>(null);
    const [selectedEndDate, setSelectedEndDate] = useState<Date | null>(null);
    const [isExpanded, setIsExpanded] = useState(false);
    // Process and validate incomes
    const validIncomes = useMemo(() => filterValidEvents(incomes), [incomes]);

    const filteredIncomes = useMemo(() => {
        return validIncomes.filter(income => {
            const incomeDate = new Date(income.dueDate);
            if (selectedStartDate && selectedEndDate) {
                return incomeDate >= selectedStartDate && incomeDate <= selectedEndDate;
            }
            if (selectedStartDate) {
                return incomeDate >= selectedStartDate;
            }
            if (selectedEndDate) {
                return incomeDate <= selectedEndDate;
            }
            return true;
        });
    }, [validIncomes, selectedStartDate, selectedEndDate]);

    const totalIncome = useMemo(() => {
        return filteredIncomes.reduce((sum, income) => sum + Number(income.amount), 0);
    }, [filteredIncomes]);

    // Prepare data for charts - Monthly income distribution
    const incomeDistributionData = useMemo(() => {
        const monthlyData: { [key: string]: number } = {};

        filteredIncomes.forEach(income => {
            const date = new Date(income.dueDate);
            const monthKey = date.toLocaleDateString(language === 'ar' ? 'ar-EG' : 'en-US', {
                month: 'short',
                year: 'numeric'
            });
            monthlyData[monthKey] = (monthlyData[monthKey] || 0) + Number(income.amount);
        });

        return Object.entries(monthlyData)
            .sort(([a], [b]) => {
                // Sort by date
                const dateA = new Date(a);
                const dateB = new Date(b);
                return dateA.getTime() - dateB.getTime();
            })
            .map(([month, amount]) => ({
                name: month,
                amount
            }));
    }, [filteredIncomes, language]);

    const categoryData = useMemo(() => {
        return processCategoryData(filteredIncomes, 'income');
    }, [filteredIncomes]);

    const monthlyIncomeData = useMemo(() => {
        const monthlyData: { [key: string]: number } = {};

        filteredIncomes.forEach(income => {
            const date = new Date(income.dueDate);
            const monthKey = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`;
            monthlyData[monthKey] = (monthlyData[monthKey] || 0) + Number(income.amount);
        });

        return Object.entries(monthlyData)
            .sort(([a], [b]) => a.localeCompare(b))
            .map(([month, amount]) => {
                const [year, monthNum] = month.split('-');
                const date = new Date(parseInt(year), parseInt(monthNum) - 1);
                return {
                    month: date.toLocaleDateString(language === 'ar' ? 'ar-EG' : 'en-US', {
                        month: 'short',
                        year: 'numeric'
                    }),
                    amount
                };
            });
    }, [filteredIncomes, language]);

    const cumulativeIncomeData = useMemo(() => {
        return filteredIncomes
            .sort((a, b) => new Date(a.dueDate).getTime() - new Date(b.dueDate).getTime())
            .reduce((acc, income, index) => {
                const cumulativeAmount = index === 0 ? Number(income.amount) : acc[index - 1].cumulativeAmount + Number(income.amount);
                acc.push({
                    date: new Date(income.dueDate).toLocaleDateString(language === 'ar' ? 'ar-EG' : 'en-US'),
                    cumulativeAmount
                });
                return acc;
            }, [] as { date: string; cumulativeAmount: number }[]);
    }, [filteredIncomes, language]);

    const { number: animatedTotalIncome } = useSpring({
        from: { number: 0 },
        number: totalIncome,
        delay: 200,
        config: { mass: 1, tension: 170, friction: 26 },
    });

    // Chart configuration
    const chartConfig = getChartConfig(language);

    // Custom tooltip component
    const CustomTooltip = ({ active, payload, label }: any) => {
        if (active && payload && payload.length) {
            return (
                <div className="bg-white p-4 border rounded-lg shadow-lg dark:bg-gray-800 dark:border-gray-700">
                    <p className="font-semibold text-gray-900 dark:text-white mb-2">{label}</p>
                    {payload.map((entry: any, index: number) => (
                        <p key={index} className="text-sm" style={{ color: entry.color }}>
                            {entry.name}: {formatCurrency(entry.value, language)}
                        </p>
                    ))}
                </div>
            );
        }
        return null;
    };

    if (!incomes || incomes.length === 0) {
        return (
            <div className="bg-white p-6 border rounded-lg shadow-sm dark:bg-gray-800 dark:border-gray-700">
                <div className="text-center py-12">
                    <div className="w-16 h-16 bg-green-100 dark:bg-green-900/20 rounded-lg mx-auto mb-4 flex items-center justify-center">
                        <div className="w-8 h-8 bg-green-500 rounded"></div>
                    </div>
                    <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
                        {t("No Income Data")}
                    </h3>
                    <p className="text-gray-500 dark:text-gray-400">
                        {t("Add income events to see analytics")}
                    </p>
                </div>
            </div>
        );
    }

    return (
        <div className="bg-white border rounded-lg shadow-sm dark:bg-gray-800 dark:border-gray-700">
            <div className="p-6 border-b dark:border-gray-700">
                <div className="flex justify-between items-center">
                    <div className="flex items-center space-x-4">
                        <div>
                            <p className="text-sm text-gray-500 dark:text-gray-400">{t("Total Income")}</p>
                            <animated.p className="text-2xl font-bold text-green-600 dark:text-green-400">
                                {animatedTotalIncome.to(n => formatCurrency(n, language))}
                            </animated.p>
                        </div>
                        <div className="text-sm text-gray-500 dark:text-gray-400">
                            <p>{filteredIncomes.length} {t("transactions")}</p>
                            <p>{t("Average")}: {formatCurrency(totalIncome / (filteredIncomes.length || 1), language)}</p>
                        </div>
                    </div>
                    <button
                        className="flex items-center space-x-2 px-4 py-2 bg-gray-100 dark:bg-gray-700 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors"
                        onClick={() => setIsExpanded(!isExpanded)}
                    >
                        <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                            {isExpanded ? t("Hide Details") : t("Show Details")}
                        </span>
                        {isExpanded ? <FaChevronUp size={16} /> : <FaChevronDown size={16} />}
                    </button>
                </div>
            </div>

            {isExpanded && (
                <div className="p-6">
                    {/* Monthly Income Distribution */}
                    <div className="mb-8">
                        <h3 className="text-lg font-semibold dark:text-white mb-4">{t("Monthly Income Distribution")}</h3>
                        <div className="h-80 w-full overflow-hidden">
                            <ResponsiveContainer width="100%" height="100%">
                                <BarChart
                                    data={incomeDistributionData}
                                    margin={{ top: 20, right: 30, left: 40, bottom: 60 }}
                                >
                                    <defs>
                                        <linearGradient id="incomeBarGradient" x1="0" y1="0" x2="0" y2="1">
                                            <stop offset="5%" stopColor={CHART_COLORS.income} stopOpacity={0.8}/>
                                            <stop offset="95%" stopColor={CHART_COLORS.income} stopOpacity={0.3}/>
                                        </linearGradient>
                                    </defs>
                                    <CartesianGrid strokeDasharray="3 3" opacity={0.3} />
                                    <XAxis
                                        dataKey="name"
                                        axisLine={false}
                                        tickLine={false}
                                        tick={{ fontSize: 11, fill: '#6b7280' }}
                                        angle={-45}
                                        textAnchor="end"
                                        height={60}
                                        interval={0}
                                    />
                                    <YAxis
                                        tickFormatter={(value) => formatNumber(value, language)}
                                        axisLine={false}
                                        tickLine={false}
                                        tick={{ fontSize: 11, fill: '#6b7280' }}
                                        width={60}
                                    />
                                    <Tooltip content={<CustomTooltip />} />
                                    <Bar
                                        dataKey="amount"
                                        fill="url(#incomeBarGradient)"
                                        radius={[4, 4, 0, 0]}
                                        maxBarSize={60}
                                    />
                                </BarChart>
                            </ResponsiveContainer>
                        </div>
                    </div>

                    <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                        {/* Income Categories */}
                        <div>
                            <h3 className="text-lg font-semibold dark:text-white mb-4">{t("Income Categories")}</h3>
                            <div className="h-80">
                                <ResponsiveContainer width="100%" height="100%">
                                    <PieChart>
                                        <Pie
                                            data={categoryData}
                                            dataKey="value"
                                            nameKey="name"
                                            cx="50%"
                                            cy="50%"
                                            outerRadius={100}
                                            label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                                            labelLine={false}
                                        >
                                            {categoryData.map((entry, index) => (
                                                <Cell key={`cell-${index}`} fill={entry.color} />
                                            ))}
                                        </Pie>
                                        <Tooltip
                                            formatter={(value) => formatCurrency(value as number, language)}
                                        />
                                        <Legend />
                                    </PieChart>
                                </ResponsiveContainer>
                            </div>
                        </div>

                        {/* Monthly Trend */}
                        <div>
                            <h3 className="text-lg font-semibold dark:text-white mb-4">{t("Monthly Income Trend")}</h3>
                            <div className="h-80">
                                <ResponsiveContainer width="100%" height="100%">
                                    <LineChart
                                        data={monthlyIncomeData}
                                        margin={chartConfig.margin}
                                    >
                                        <CartesianGrid {...chartConfig.cartesianGrid} />
                                        <XAxis
                                            dataKey="month"
                                            {...chartConfig.xAxis}
                                        />
                                        <YAxis
                                            tickFormatter={(value) => formatNumber(value, language)}
                                            {...chartConfig.yAxis}
                                        />
                                        <Tooltip content={<CustomTooltip />} />
                                        <Line
                                            type="monotone"
                                            dataKey="amount"
                                            stroke={CHART_COLORS.income}
                                            strokeWidth={3}
                                            dot={{ fill: CHART_COLORS.income, strokeWidth: 2, r: 6 }}
                                            activeDot={{ r: 8, stroke: CHART_COLORS.income, strokeWidth: 2 }}
                                        />
                                    </LineChart>
                                </ResponsiveContainer>
                            </div>
                        </div>
                    </div>

                    {/* Cumulative Income */}
                    {cumulativeIncomeData.length > 0 && (
                        <div className="mt-8">
                            <h3 className="text-lg font-semibold dark:text-white mb-4">{t("Cumulative Income Over Time")}</h3>
                            <div className="h-80">
                                <ResponsiveContainer width="100%" height="100%">
                                    <LineChart
                                        data={cumulativeIncomeData}
                                        margin={chartConfig.margin}
                                    >
                                        <defs>
                                            <linearGradient id="cumulativeIncomeGradient" x1="0" y1="0" x2="0" y2="1">
                                                <stop offset="5%" stopColor={CHART_COLORS.income} stopOpacity={0.6}/>
                                                <stop offset="95%" stopColor={CHART_COLORS.income} stopOpacity={0.1}/>
                                            </linearGradient>
                                        </defs>
                                        <CartesianGrid {...chartConfig.cartesianGrid} />
                                        <XAxis
                                            dataKey="date"
                                            {...chartConfig.xAxis}
                                        />
                                        <YAxis
                                            tickFormatter={(value) => formatNumber(value, language)}
                                            {...chartConfig.yAxis}
                                        />
                                        <Tooltip content={<CustomTooltip />} />
                                        <Line
                                            type="monotone"
                                            dataKey="cumulativeAmount"
                                            stroke={CHART_COLORS.income}
                                            strokeWidth={3}
                                            fill="url(#cumulativeIncomeGradient)"
                                            dot={{ fill: CHART_COLORS.income, strokeWidth: 2, r: 4 }}
                                        />
                                    </LineChart>
                                </ResponsiveContainer>
                            </div>
                        </div>
                    )}

                    {/* Summary Statistics */}
                    <div className="mt-8 pt-6 border-t dark:border-gray-700">
                        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                            <div className="text-center">
                                <p className="text-sm text-gray-500 dark:text-gray-400">{t("Best Month")}</p>
                                <p className="text-lg font-semibold text-green-600 dark:text-green-400">
                                    {incomeDistributionData.length > 0
                                        ? incomeDistributionData.reduce((best, current) =>
                                            current.amount > best.amount ? current : best,
                                            incomeDistributionData[0]
                                          )?.name || '-'
                                        : '-'
                                    }
                                </p>
                            </div>
                            <div className="text-center">
                                <p className="text-sm text-gray-500 dark:text-gray-400">{t("Average Monthly")}</p>
                                <p className="text-lg font-semibold text-blue-600 dark:text-blue-400">
                                    {incomeDistributionData.length > 0
                                        ? formatCurrency(
                                            incomeDistributionData.reduce((sum, item) => sum + item.amount, 0) / incomeDistributionData.length,
                                            language
                                          )
                                        : formatCurrency(0, language)
                                    }
                                </p>
                            </div>
                            <div className="text-center">
                                <p className="text-sm text-gray-500 dark:text-gray-400">{t("Categories")}</p>
                                <p className="text-lg font-semibold text-purple-600 dark:text-purple-400">
                                    {categoryData.length}
                                </p>
                            </div>
                            <div className="text-center">
                                <p className="text-sm text-gray-500 dark:text-gray-400">{t("This Month")}</p>
                                <p className="text-lg font-semibold text-orange-600 dark:text-orange-400">
                                    {monthlyIncomeData.length > 0
                                        ? formatCurrency(monthlyIncomeData[monthlyIncomeData.length - 1]?.amount || 0, language)
                                        : formatCurrency(0, language)
                                    }
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            )}
        </div>
    );
};

export default IncomeSummary;