{"Add New Event": "Add New Event", "Title": "Title", "Description": "Description", "Type": "Type", "Amount": "Amount", "Priority": "Priority", "Status": "Status", "Date": "Date", "Contact": "Contact", "Location": "Location", "Save": "Save", "Cancel": "Cancel", "Add Contact": "Add Contact", "Add Location": "Add Location", "Select Contact": "Select Contact", "Select Location": "Select Location", "Category": "Category", "Expense": "Expense", "Income": "Income", "Due Date": "Due Date", "Completed": "Completed", "Not Started": "Not Started", "In Progress": "In Progress", "High": "High", "Medium": "Medium", "Low": "Low", "Select Type": "Select Type", "Pending": "Pending", "Cancelled": "Cancelled", "Overdue": "Overdue", "Upcoming": "Upcoming", "Recursive": "Recursive", "Recurrence Settings": "Recurrence Settings", "Configure how often this event should recur.": "Configure how often this event should recur.", "Recurrence Type": "Recurrence Type", "Daily": "Daily", "Weekly": "Weekly", "Monthly": "Monthly", "Yearly": "Yearly", "Recurrence Count": "Recurrence Count", "Recurrence Off": "Recurrence Off", "Recurrence On": "Recurrence On", "Max Recurrence Time": "Max Recurrence Time", "Select date": "Select date", "Add New Type": "Add New Type", "Add New Contact": "Add New Contact", "Name": "Name", "Phone": "Phone", "Email": "Email", "Add New Location": "Add New Location", "Address": "Address", "City": "City", "State": "State", "Country": "Country", "Postal Code": "Postal Code", "last edited": "last edited", "last edited by": "last edited by", "income": "Income", "city": "City", "country": "Country", "postal code": "Postal Code", "financialHistory": "Financial History", "no description available": "No Description Available", "events report": "Events Report", "total expenses": "Total Expenses", "generate pdf": "Generate Report", "Events": "Events", "Notification Date": "Notification Date", "Remove": "Remove", "Please fill all fields (Title, Amount, Due Date, and Type) before enabling recursive": "Please fill all fields (Title, Amount, Due Date, and Type) before enabling recursive", "Auto-generate events?": "Auto-generate events?", "Do you want to auto-generate": "Do you want to auto-generate", "based on this location?": "based on this location?", "Is this the Total Amount?": "Is this the Total Amount?", "Is the amount you entered the total amount or just your cut from the location": "Is the amount you entered the total amount or just your cut from the location?", "Save Event": "Save Event", "Normal Event": "Normal Event", "Group Event": "Group Event", "Import Data": "Import Data"}