import React, { useState, useMemo } from 'react';
import { EventDetails } from '@/lib/interfaces/finaces';
import useLanguage from '@/hooks/useLanguage';
import AnalyticsSection from './Sections/AnalyticsSection';
import IncomeVsExpenseSection from './Sections/IncomeVsExpenseSection';
import AdvancedAnalyticsSection from './Sections/AdvancedAnalyticsSection';
import IncomeSummary from '../Financials/Income/IncomeSummary';
import ExpenseSummary from '../Financials/Expenses/ExpenseSummary';
import { 
  SafeAnalyticsWrapper, 
  AnalyticsEmptyState, 
  AnalyticsLoading,
  useAnalyticsErrorHandler 
} from '../common/AnalyticsErrorBoundary';
import { filterValidEvents } from '@/utils/analyticsUtils';
import { BarChart3, TrendingUp, DollarSign, Target, Settings } from 'lucide-react';

interface ImprovedDashboardProps {
  events: EventDetails[];
  isLoading?: boolean;
}

const ImprovedDashboard: React.FC<ImprovedDashboardProps> = ({ events, isLoading = false }) => {
  const { t, language } = useLanguage();
  const [activeTab, setActiveTab] = useState<'overview' | 'income' | 'expenses' | 'advanced'>('overview');
  const [dateRange, setDateRange] = useState<'1m' | '3m' | '6m' | '12m' | 'all'>('12m');
  const { error, clearError } = useAnalyticsErrorHandler();

  // Process and filter events
  const { validEvents, incomeEvents, expenseEvents, filteredEvents } = useMemo(() => {
    const valid = filterValidEvents(events);
    const income = valid.filter(event => event.category === 'income');
    const expense = valid.filter(event => event.category === 'expense');
    
    // Apply date filtering
    let filtered = valid;
    if (dateRange !== 'all') {
      const months = parseInt(dateRange.replace('m', ''));
      const cutoffDate = new Date();
      cutoffDate.setMonth(cutoffDate.getMonth() - months);
      filtered = valid.filter(event => new Date(event.dueDate) >= cutoffDate);
    }
    
    return {
      validEvents: valid,
      incomeEvents: income,
      expenseEvents: expense,
      filteredEvents: filtered
    };
  }, [events, dateRange]);

  // Calculate summary statistics
  const summaryStats = useMemo(() => {
    const totalIncome = incomeEvents.reduce((sum, event) => sum + Number(event.amount), 0);
    const totalExpense = expenseEvents.reduce((sum, event) => sum + Number(event.amount), 0);
    const netProfit = totalIncome - totalExpense;
    const transactionCount = validEvents.length;
    
    return {
      totalIncome,
      totalExpense,
      netProfit,
      transactionCount,
      profitMargin: totalIncome > 0 ? (netProfit / totalIncome) * 100 : 0
    };
  }, [incomeEvents, expenseEvents, validEvents]);

  if (isLoading) {
    return (
      <div className="space-y-6">
        <AnalyticsLoading />
        <AnalyticsLoading />
      </div>
    );
  }

  if (error) {
    return (
      <AnalyticsEmptyState
        title={t("Analytics Error")}
        description={t("There was an error loading the analytics. Please try refreshing the page.")}
        icon={<BarChart3 className="w-16 h-16 text-red-400 mx-auto mb-4" />}
        action={
          <button
            onClick={clearError}
            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            {t("Try Again")}
          </button>
        }
      />
    );
  }

  return (
    <div className="space-y-6">
      {/* Header with Summary Cards */}
      <div className="bg-white border rounded-lg shadow-sm dark:bg-gray-800 dark:border-gray-700">
        <div className="p-6">
          <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between mb-6">
            <h1 className="text-2xl font-bold text-gray-900 dark:text-white mb-4 lg:mb-0">
              {t("Financial Dashboard")}
            </h1>
            
            {/* Date Range Filter */}
            <div className="flex bg-gray-100 dark:bg-gray-700 rounded-lg p-1">
              {[
                { key: '1m', label: t('1M') },
                { key: '3m', label: t('3M') },
                { key: '6m', label: t('6M') },
                { key: '12m', label: t('12M') },
                { key: 'all', label: t('All') }
              ].map((option) => (
                <button
                  key={option.key}
                  onClick={() => setDateRange(option.key as any)}
                  className={`px-3 py-2 rounded-md text-sm font-medium transition-colors ${
                    dateRange === option.key
                      ? "bg-white dark:bg-gray-600 text-gray-900 dark:text-white shadow-sm"
                      : "text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white"
                  }`}
                >
                  {option.label}
                </button>
              ))}
            </div>
          </div>

          {/* Summary Cards */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <div className="bg-gradient-to-r from-green-50 to-green-100 dark:from-green-900/20 dark:to-green-800/20 p-6 rounded-lg overflow-hidden">
              <div className="flex items-start justify-between min-h-[100px]">
                <div className="flex-1 min-w-0 pr-4">
                  <p className="text-sm text-green-600 dark:text-green-400 mb-2">{t("Total Income")}</p>
                  <p className="text-2xl font-bold text-green-700 dark:text-green-300 truncate">
                    {summaryStats.totalIncome.toLocaleString(language === 'ar' ? 'ar-EG' : 'en-US', {
                      style: 'currency',
                      currency: 'EGP',
                      minimumFractionDigits: 0
                    })}
                  </p>
                  <p className="text-xs text-green-600 dark:text-green-400 mt-1">
                    {incomeEvents.length} {t("transactions")}
                  </p>
                </div>
                <div className="flex-shrink-0">
                  <DollarSign className="w-8 h-8 text-green-500" />
                </div>
              </div>
            </div>

            <div className="bg-gradient-to-r from-red-50 to-red-100 dark:from-red-900/20 dark:to-red-800/20 p-6 rounded-lg overflow-hidden">
              <div className="flex items-start justify-between min-h-[100px]">
                <div className="flex-1 min-w-0 pr-4">
                  <p className="text-sm text-red-600 dark:text-red-400 mb-2">{t("Total Expense")}</p>
                  <p className="text-2xl font-bold text-red-700 dark:text-red-300 truncate">
                    {summaryStats.totalExpense.toLocaleString(language === 'ar' ? 'ar-EG' : 'en-US', {
                      style: 'currency',
                      currency: 'EGP',
                      minimumFractionDigits: 0
                    })}
                  </p>
                  <p className="text-xs text-red-600 dark:text-red-400 mt-1">
                    {expenseEvents.length} {t("transactions")}
                  </p>
                </div>
                <div className="flex-shrink-0">
                  <TrendingUp className="w-8 h-8 text-red-500" />
                </div>
              </div>
            </div>

            <div className={`bg-gradient-to-r p-6 rounded-lg overflow-hidden ${
              summaryStats.netProfit >= 0
                ? 'from-blue-50 to-blue-100 dark:from-blue-900/20 dark:to-blue-800/20'
                : 'from-orange-50 to-orange-100 dark:from-orange-900/20 dark:to-orange-800/20'
            }`}>
              <div className="flex items-start justify-between min-h-[100px]">
                <div className="flex-1 min-w-0 pr-4">
                  <p className={`text-sm mb-2 ${
                    summaryStats.netProfit >= 0
                      ? 'text-blue-600 dark:text-blue-400'
                      : 'text-orange-600 dark:text-orange-400'
                  }`}>
                    {t("Net Profit")}
                  </p>
                  <p className={`text-2xl font-bold truncate ${
                    summaryStats.netProfit >= 0
                      ? 'text-blue-700 dark:text-blue-300'
                      : 'text-orange-700 dark:text-orange-300'
                  }`}>
                    {summaryStats.netProfit.toLocaleString(language === 'ar' ? 'ar-EG' : 'en-US', {
                      style: 'currency',
                      currency: 'EGP',
                      minimumFractionDigits: 0
                    })}
                  </p>
                  <p className="text-xs text-gray-600 dark:text-gray-400 mt-1 whitespace-nowrap">
                    {summaryStats.profitMargin.toFixed(1)}% {t("margin")}
                  </p>
                </div>
                <div className="flex-shrink-0">
                  <Target className="w-8 h-8 text-blue-500" />
                </div>
              </div>
            </div>

            <div className="bg-gradient-to-r from-purple-50 to-purple-100 dark:from-purple-900/20 dark:to-purple-800/20 p-6 rounded-lg overflow-hidden">
              <div className="flex items-start justify-between min-h-[100px]">
                <div className="flex-1 min-w-0 pr-4">
                  <p className="text-sm text-purple-600 dark:text-purple-400 mb-2">{t("Total Transactions")}</p>
                  <p className="text-2xl font-bold text-purple-700 dark:text-purple-300">
                    {summaryStats.transactionCount}
                  </p>
                  <p className="text-xs text-purple-600 dark:text-purple-400 mt-1">
                    {t("in selected period")}
                  </p>
                </div>
                <div className="flex-shrink-0">
                  <BarChart3 className="w-8 h-8 text-purple-500" />
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Navigation Tabs */}
        <div className="border-t dark:border-gray-700">
          <nav className="flex space-x-8 px-6" aria-label="Tabs">
            {[
              { key: 'overview', label: t('Overview'), icon: BarChart3 },
              { key: 'income', label: t('Income'), icon: DollarSign },
              { key: 'expenses', label: t('Expenses'), icon: TrendingUp },
              { key: 'advanced', label: t('Advanced'), icon: Settings }
            ].map((tab) => (
              <button
                key={tab.key}
                onClick={() => setActiveTab(tab.key as any)}
                className={`flex items-center space-x-2 py-4 px-1 border-b-2 font-medium text-sm ${
                  activeTab === tab.key
                    ? 'border-blue-500 text-blue-600 dark:text-blue-400'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300'
                }`}
              >
                <tab.icon className="w-4 h-4" />
                <span>{tab.label}</span>
              </button>
            ))}
          </nav>
        </div>
      </div>

      {/* Tab Content */}
      <div className="space-y-6">
        {activeTab === 'overview' && (
          <>
            <SafeAnalyticsWrapper data={filteredEvents}>
              <AnalyticsSection events={filteredEvents} />
            </SafeAnalyticsWrapper>
            
            <SafeAnalyticsWrapper data={filteredEvents}>
              <IncomeVsExpenseSection events={filteredEvents} />
            </SafeAnalyticsWrapper>
          </>
        )}

        {activeTab === 'income' && (
          <SafeAnalyticsWrapper data={incomeEvents}>
            <IncomeSummary incomes={incomeEvents} />
          </SafeAnalyticsWrapper>
        )}

        {activeTab === 'expenses' && (
          <SafeAnalyticsWrapper data={expenseEvents}>
            <ExpenseSummary expenses={expenseEvents} />
          </SafeAnalyticsWrapper>
        )}

        {activeTab === 'advanced' && (
          <SafeAnalyticsWrapper data={filteredEvents}>
            <AdvancedAnalyticsSection events={filteredEvents} />
          </SafeAnalyticsWrapper>
        )}
      </div>
    </div>
  );
};

export default ImprovedDashboard;
