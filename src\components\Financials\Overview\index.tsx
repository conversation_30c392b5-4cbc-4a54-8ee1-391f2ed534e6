"use client";
import React, { useState, useEffect, useCallback } from "react";
import { useSearchParams } from "next/navigation"; // Import useSearchParams
import Breadcrumb from "@/components/Breadcrumbs/Breadcrumb";
import useLanguage from "@/hooks/useLanguage";
import { EventDetails } from "@/lib/interfaces/finaces";
import UpcomingEventPopup from "../common/UpcommingEventPopup.tsx";
import Analytics from "./Sections/Analytic";
import UpcomingEventsPage from "@/components/Dashboard/UpcommingEventsPage/UpcommingEvents";
import { getDateRange, DateRangeSelector } from "@/components/Financials/common/dateRanges"; 
import DatePicker from "react-datepicker";
import "react-datepicker/dist/react-datepicker.css";
import { enUS, arSA } from "date-fns/locale";
import getCombinedEvents from "@/lib/events";
import { LoadingComp } from "@/components/common/Loading";
import { format } from "date-fns";


const Overview: React.FC = () => {
  const { t, language } = useLanguage();
  const searchParams = useSearchParams(); 

  // Calculate date range
  const currentDate = new Date();
  const startRangeDate = new Date(currentDate);
  startRangeDate.setDate(currentDate.getDate() - 15);
  const endRangeDate = new Date(currentDate);
  endRangeDate.setDate(currentDate.getDate() + 15);

  const formatDate = (date: Date) => {
    return date.toISOString().split('T')[0];
  };

  const [upcomingEvents, setUpcomingEvents] = useState<EventDetails[]>([]);
  const [selectedEvent, setSelectedEvent] = useState<EventDetails | null>(null);
  const [filterStatus, setFilterStatus] = useState("all");
  const [filterPriority, setFilterPriority] = useState("all");
  const [startDate, setStartDate] = useState<Date>(startRangeDate);
  const [endDate, setEndDate] = useState<Date>(endRangeDate);
  const [loading, setLoading] = useState<boolean>(true); // Loading state

  // Get month and year from URL parameters - add null check
  const monthParam = searchParams ? searchParams.get("month") : null;
  const yearParam = searchParams ? searchParams.get("year") : null;

  useEffect(() => {
    if (monthParam && yearParam) {
      const month = parseInt(monthParam, 10) - 1; // Convert to zero-indexed month
      const year = parseInt(yearParam, 10);
      const start = new Date(year, month, 1);
      const end = new Date(year, month + 1, 0); // Last day of the month

      setStartDate(start);
      setEndDate(end);
    }
  }, [monthParam, yearParam]);
  useEffect(() => {
    const fetchEvents = async () => {
      setLoading(true);
      try {
        const events = await getCombinedEvents();
        console.log("sdlfkasdjkhfkasdgjk");
        
        setUpcomingEvents(events);
      } catch (error) {
        console.error("Error fetching events:", error);
      } finally {
        setLoading(false);
      }
    };

    fetchEvents();
  }, []);

  const filterEventsByDate = (events: EventDetails[]) => {
    return events.filter(event => {
      const eventDate = new Date(event.dueDate);
      const start = startDate ? new Date(startDate) : null;
      const end = endDate ? new Date(endDate) : null;

      if (start && end) {
        return eventDate >= start && eventDate <= end;
      } else if (start) {
        return eventDate >= start;
      } else if (end) {
        return eventDate <= end;
      } else {
        return true;
      }
    });
  };
  const convertToArabicNumerals = (num: number | string | undefined | null) => {
    if (num === undefined || num === null) return "";
    return Math.floor(Number(num)).toString().replace(/\d/g, (d) => "٠١٢٣٤٥٦٧٨٩"[parseInt(d)]);
};
const formatDateLocalized = (date: Date | null) => {
  if (!date) return "";
  return language === "ar"
    ? `${convertToArabicNumerals(date.getDate())}/${convertToArabicNumerals(date.getMonth() + 1)}/${convertToArabicNumerals(date.getFullYear())}`
    : date.toLocaleDateString("en-US", {
        year: "numeric",
        month: "2-digit",
        day: "2-digit",
      });
};


  // Event handlers
  const handleEditClick = useCallback((id: string) => {
    const event = upcomingEvents.find((event) => event.id === id);
    if (event) {
      setSelectedEvent(event);
    }
  }, [upcomingEvents]);

  const handlePopupClose = useCallback(() => setSelectedEvent(null), []);
  const handleSave = useCallback((updatedEvent: EventDetails) => {
    setUpcomingEvents((prevEvents) =>
      prevEvents.map((event) => (event.id === updatedEvent.id ? updatedEvent : event))
    );
    handlePopupClose();
  }, [handlePopupClose]);

  const handleDateRangeChange = (range: string) => {
    const { start, end } = getDateRange(range);
    setStartDate(new Date(start));
    setEndDate(new Date(end));
  };

  const locale = language === "ar" ? arSA : enUS;

  return (
    <>
      <Breadcrumb pageName={t("overview")} />
      <h2 className="text-2xl font-bold text-gray-900 dark:text-gray-400 mb-4 flex items-center">
        {t("financialOverview")}
            
          <span className="text-sm font-normal text-gray-600 ml-2 flex items-center">
            <span className="mr-1">{t("as of")}</span>
            <DatePicker
              selected={startDate}
              onChange={(date: Date | null) => date && setStartDate(date)}
              locale={locale}
              calendarStartDay={6} 
              customInput={
                <input
                  type="text"
                  className="border rounded p-2 dark:bg-gray-800 dark:text-gray-200 dark:border-gray-600"
                  value={"asdkfhkjsd"}
                  readOnly
                />
              }
            />

            <span className="mx-1">{t("to")}</span>
            <DatePicker
              selected={endDate}
              onChange={(date: Date | null) => date && setEndDate(date)}
              className="border rounded p-2 ml-1 dark:bg-gray-800 dark:text-gray-200 dark:border-gray-600"
              locale={locale}
              customInput={
                <input
                  type="text"
                  className="border rounded p-2 dark:bg-gray-800 dark:text-gray-200 dark:border-gray-600"
                  value={formatDateLocalized(endDate)}
                  readOnly
                />
              }
            />
          </span>
      </h2>
      <DateRangeSelector onChange={handleDateRangeChange} />
      <Analytics events={filterEventsByDate(upcomingEvents)} />
      {loading ? (
        <div className="fixed top-0 left-0 z-9999 flex h-screen w-full items-center justify-center bg-black dark:bg-boxdark">
          <LoadingComp />
          </div>
      ):(
        <UpcomingEventsPage events={filterEventsByDate(upcomingEvents)} setEvents={setUpcomingEvents} />

      )  
        }
      {selectedEvent && (
        <UpcomingEventPopup event={selectedEvent} onClose={handlePopupClose} onSave={handleSave} />
      )}
    </>
  );
};

export default Overview;