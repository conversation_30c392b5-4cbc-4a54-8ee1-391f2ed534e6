import React from "react";
import { Contact, Location } from "@/lib/interfaces/finaces";
import { ReservationFormData } from "@/lib/interfaces/reservation";
import { FaInfoCircle, FaCheck } from "react-icons/fa";

interface ReservationFormClientLocationProps {
  formData: ReservationFormData;
  selectedContact: Contact | null;
  setSelectedContact: (contact: Contact | null) => void;
  selectedLocation: {
    id: string;
    totalCapacity: number;
    takenCapacity: number;
    availableCapacity: number;
  } | null;
  contacts: Contact[] | undefined;
  locationList: any[] | undefined;
  handleInputChange: (
    e: React.ChangeEvent<
      HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement
    >,
  ) => void;
  language: string;
  t: (key: string) => string;
  reservation?: any; // Add reservation prop to access required_capacity
}

const ReservationFormClientLocation: React.FC<
  ReservationFormClientLocationProps
> = ({
  formData,
  selectedContact,
  setSelectedContact,
  selectedLocation,
  contacts,
  locationList,
  handleInputChange,
  language,
  t,
  reservation, // Add reservation parameter
}) => {
  return (
    <div className="rounded-lg border border-gray-200 bg-gray-50 p-4 dark:border-gray-700 dark:bg-gray-900 sm:p-6">
      <h3 className="mb-4 text-lg font-semibold text-gray-800 dark:text-gray-200">
        {t("Client & Location Details")}
      </h3>

      <div className="grid grid-cols-1 gap-6 lg:grid-cols-2">
        {/* Client Selection */}
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
              {t("Client")} <span className="text-red-500">*</span>
            </label>
            <select
              name="contactId"
              value={formData.contactId || ""} // Use formData.contactId instead of selectedContact.id
              onChange={(e) => {
                const selectedContact = contacts?.find(
                  (contact) => contact.id === e.target.value,
                );
                setSelectedContact(selectedContact || null);
                // Also update formData
                handleInputChange(e);
              }}
              className="mt-1 block w-full rounded-md border-gray-300 px-3 py-2 shadow-sm focus:border-blue-500 focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-700 dark:text-gray-200 sm:text-sm"
              required
            >
              <option value="">{t("Select a client")}</option>
              {contacts?.map((contact) => (
                <option key={contact.id} value={contact.id}>
                  {contact.name}
                </option>
              ))}
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
              {t("Location")} <span className="text-red-500">*</span>
            </label>
            <select
              name="locationId"
              value={formData.locationId || ""} // Ensure we're using formData.locationId
              onChange={handleInputChange}
              className="mt-1 block w-full rounded-md border-gray-300 px-3 py-2 shadow-sm focus:border-blue-500 focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-700 dark:text-gray-200 sm:text-sm"
              required
            >
              <option value="">{t("Select a location")}</option>
              {locationList?.map((location) => (
                <option key={location.id} value={location.id}>
                  {location.name}
                </option>
              ))}
            </select>
          </div>

          {/* Display selected contact information when editing */}
          {(selectedContact || formData.contactId) && (
            <div className="rounded-lg bg-blue-50 p-3 dark:bg-blue-900/20">
              <h4 className="text-sm font-medium text-blue-800 dark:text-blue-200">
                {t("Selected Client")}
              </h4>
              <div className="mt-2 space-y-1">
                <p className="text-sm text-blue-700 dark:text-blue-300">
                  <strong>{t("Name")}:</strong>{" "}
                  {selectedContact?.name ||
                    contacts?.find((c) => c.id === formData.contactId)?.name ||
                    t("Contact information not available")}
                </p>
                {(selectedContact?.email ||
                  contacts?.find((c) => c.id === formData.contactId)
                    ?.email) && (
                  <p className="text-sm text-blue-700 dark:text-blue-300">
                    <strong>{t("Email")}:</strong>{" "}
                    {selectedContact?.email ||
                      contacts?.find((c) => c.id === formData.contactId)?.email}
                  </p>
                )}
                {(selectedContact?.phone ||
                  contacts?.find((c) => c.id === formData.contactId)
                    ?.phone) && (
                  <p className="text-sm text-blue-700 dark:text-blue-300">
                    <strong>{t("Phone")}:</strong>{" "}
                    {selectedContact?.phone ||
                      contacts?.find((c) => c.id === formData.contactId)?.phone}
                  </p>
                )}
              </div>
            </div>
          )}

          {/* Display selected location information when editing */}
          {formData.locationId && locationList && (
            <div className="rounded-lg bg-green-50 p-3 dark:bg-green-900/20">
              <h4 className="text-sm font-medium text-green-800 dark:text-green-200">
                {t("Selected Location")}
              </h4>
              <div className="mt-2">
                {(() => {
                  const selectedLocationInfo = locationList.find(
                    (l) => l.id === formData.locationId,
                  );
                  return selectedLocationInfo ? (
                    <div className="space-y-1">
                      <p className="text-sm text-green-700 dark:text-green-300">
                        <strong>{t("Name")}:</strong>{" "}
                        {selectedLocationInfo.name}
                      </p>
                      <p className="text-sm text-green-700 dark:text-green-300">
                        <strong>{t("Address")}:</strong>{" "}
                        {selectedLocationInfo.address}
                      </p>
                      {selectedLocationInfo.city && (
                        <p className="text-sm text-green-700 dark:text-green-300">
                          <strong>{t("City")}:</strong>{" "}
                          {selectedLocationInfo.city}
                        </p>
                      )}
                      {selectedLocationInfo.country && (
                        <p className="text-sm text-green-700 dark:text-green-300">
                          <strong>{t("Country")}:</strong>{" "}
                          {selectedLocationInfo.country}
                        </p>
                      )}
                      {/* Show required capacity from reservation */}
                      {(reservation?.required_capacity ||
                        formData.required_capacity) && (
                        <p className="text-sm text-green-700 dark:text-green-300">
                          <strong>{t("Required Capacity")}:</strong>{" "}
                          {reservation?.required_capacity ||
                            formData.required_capacity}
                        </p>
                      )}
                      {/* Show current reservation's capacity usage */}
                      {selectedLocation && (
                        <div className="mt-2 rounded bg-green-100 p-2 dark:bg-green-800">
                          <p className="text-xs text-green-800 dark:text-green-200">
                            <strong>{t("Current Capacity")}:</strong>{" "}
                            {selectedLocation.takenCapacity}/
                            {selectedLocation.totalCapacity} (
                            {selectedLocation.availableCapacity}{" "}
                            {t("available")})
                          </p>
                        </div>
                      )}
                    </div>
                  ) : (
                    <p className="text-sm text-green-700 dark:text-green-300">
                      {t("Location information not available")}
                    </p>
                  );
                })()}
              </div>
            </div>
          )}

          {/* Show reservation capacity summary when editing */}
          {reservation && (
            <div className="rounded-lg bg-indigo-50 p-3 dark:bg-indigo-900/20">
              <h4 className="text-sm font-medium text-indigo-800 dark:text-indigo-200">
                {t("Reservation Capacity Info")}
              </h4>
              <div className="mt-2 space-y-1">
                <p className="text-sm text-indigo-700 dark:text-indigo-300">
                  <strong>{t("Required Capacity")}:</strong>{" "}
                  {reservation.required_capacity ||
                    reservation.required_capacity ||
                    formData.required_capacity}
                </p>
                {selectedLocation && (
                  <p className="text-sm text-indigo-700 dark:text-indigo-300">
                    <strong>{t("Percentage of Total")}:</strong>{" "}
                    {selectedLocation.totalCapacity > 0
                      ? (
                          ((reservation.required_capacity ||
                            reservation.required_capacity ||
                            formData.required_capacity) /
                            selectedLocation.totalCapacity) *
                          100
                        ).toFixed(1)
                      : 0}
                    %
                  </p>
                )}
              </div>
            </div>
          )}
        </div>

        {/* Capacity Information */}
        <div>
          {selectedLocation && formData.startDate && formData.endDate ? (
            <div className="rounded-lg border border-gray-200 bg-white p-6 shadow-sm dark:border-gray-600 dark:bg-gray-800">
              <div
                className={`mb-4 flex items-center ${
                  language === "ar" ? "flex-row-reverse" : ""
                } justify-between`}
              >
                <h4 className="text-lg font-semibold text-gray-900 dark:text-white">
                  {t("Location Capacity")}
                </h4>
                <div className="rounded-full bg-blue-100 px-3 py-1 text-xs font-medium text-blue-800 dark:bg-blue-900 dark:text-blue-200">
                  {selectedLocation.availableCapacity > 0
                    ? t("Available")
                    : t("Full")}
                </div>
              </div>

              {/* Capacity Stats Grid */}
              <div className="mb-6 grid grid-cols-3 gap-4">
                <div className="rounded-lg bg-gradient-to-br from-blue-50 to-blue-100 p-4 text-center dark:from-blue-900 dark:to-blue-800">
                  <div className="text-2xl font-bold text-blue-600 dark:text-blue-400">
                    {selectedLocation.totalCapacity}
                  </div>
                  <div className="text-sm font-medium text-blue-700 dark:text-blue-300">
                    {t("Total")}
                  </div>
                </div>
                <div className="rounded-lg bg-gradient-to-br from-orange-50 to-orange-100 p-4 text-center dark:from-orange-900 dark:to-orange-800">
                  <div className="text-2xl font-bold text-orange-600 dark:text-orange-400">
                    {selectedLocation.takenCapacity}
                  </div>
                  <div className="text-sm font-medium text-orange-700 dark:text-orange-300">
                    {t("Taken")}
                  </div>
                </div>
                <div
                  className={`rounded-lg p-4 text-center ${
                    selectedLocation.availableCapacity > 0
                      ? "bg-gradient-to-br from-green-50 to-green-100 dark:from-green-900 dark:to-green-800"
                      : "bg-gradient-to-br from-red-50 to-red-100 dark:from-red-900 dark:to-red-800"
                  }`}
                >
                  <div
                    className={`text-2xl font-bold ${
                      selectedLocation.availableCapacity > 0
                        ? "text-green-600 dark:text-green-400"
                        : "text-red-600 dark:text-red-400"
                    }`}
                  >
                    {selectedLocation.availableCapacity}
                  </div>
                  <div
                    className={`text-sm font-medium ${
                      selectedLocation.availableCapacity > 0
                        ? "text-green-700 dark:text-green-300"
                        : "text-red-700 dark:text-red-300"
                    }`}
                  >
                    {t("Available")}
                  </div>
                </div>
              </div>

              {/* Capacity Progress Bar */}
              <div className="mb-6">
                <div
                  className={`mb-2 flex items-center justify-between text-sm ${
                    language === "ar" ? "flex-row-reverse" : ""
                  }`}
                >
                  <span className="font-medium text-gray-700 dark:text-gray-300">
                    {t("Capacity Usage")}
                  </span>
                  <span className="text-gray-600 dark:text-gray-400">
                    {(
                      (selectedLocation.takenCapacity /
                        selectedLocation.totalCapacity) *
                      100
                    ).toFixed(1)}
                    %
                  </span>
                </div>
                <div className="relative h-4 w-full overflow-hidden rounded-full bg-gray-200 dark:bg-gray-700">
                  <div
                    className={`h-full transition-all duration-500 ease-out ${
                      selectedLocation.availableCapacity > 0
                        ? "bg-gradient-to-r from-blue-500 to-blue-600"
                        : "bg-gradient-to-r from-red-500 to-red-600"
                    }`}
                    style={{
                      width: `${(selectedLocation.takenCapacity / selectedLocation.totalCapacity) * 100}%`,
                    }}
                  >
                    <div className="absolute inset-0 animate-pulse bg-white opacity-20"></div>
                  </div>
                </div>
                <div
                  className={`mt-1 flex justify-between text-xs text-gray-500 dark:text-gray-400 ${
                    language === "ar" ? "flex-row-reverse" : ""
                  }`}
                >
                  <span>0</span>
                  <span>{selectedLocation.totalCapacity}</span>
                </div>
              </div>

              {/* Required Capacity Input */}
              <div className="space-y-3">
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                  {t("Required Capacity")}{" "}
                  <span className="text-red-500">*</span>
                </label>

                <div
                  className={`relative ${
                    language === "ar" ? "flex flex-row-reverse" : "flex"
                  }`}
                >
                  <input
                    type="number"
                    name="required_capacity"
                    value={formData.required_capacity}
                    onChange={handleInputChange}
                    min="1"
                    max={selectedLocation.availableCapacity}
                    className={`block w-full ${
                      language === "ar"
                        ? "rounded-r-lg text-right"
                        : "rounded-l-lg"
                    } border px-4 py-3 text-lg font-medium shadow-sm transition-colors focus:border-blue-500 focus:ring-2 focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-700 dark:text-gray-200 ${
                      formData.required_capacity >
                      selectedLocation.availableCapacity
                        ? "border-red-300 bg-red-50 dark:border-red-600 dark:bg-red-900"
                        : "border-gray-300 bg-white dark:border-gray-600 dark:bg-gray-700"
                    }`}
                    placeholder="0"
                    required
                  />
                  <div
                    className={`${
                      language === "ar"
                        ? "rounded-l-lg border-r-0"
                        : "rounded-r-lg border-l-0"
                    } flex items-center border border-gray-300 bg-gray-50 px-4 dark:border-gray-600 dark:bg-gray-700`}
                  >
                    <span className="text-sm font-medium text-gray-500 dark:text-gray-400">
                      / {selectedLocation.availableCapacity}
                    </span>
                  </div>
                </div>

                {/* Capacity Preview */}
                {formData.required_capacity > 0 && (
                  <div className="mt-4 rounded-lg bg-blue-50 p-4 dark:bg-blue-900">
                    <div
                      className={`mb-2 flex items-center justify-between text-sm ${
                        language === "ar" ? "flex-row-reverse" : ""
                      }`}
                    >
                      <span className="font-medium text-blue-800 dark:text-blue-200">
                        {t("Your Reservation")}
                      </span>
                      <span className="text-blue-600 dark:text-blue-400">
                        {(
                          (formData.required_capacity /
                            selectedLocation.availableCapacity) *
                          100
                        ).toFixed(1)}
                        % {t("of available capacity")}
                      </span>
                    </div>
                    <div className="relative h-3 w-full overflow-hidden rounded-full bg-blue-200 dark:bg-blue-800">
                      <div
                        className="h-full bg-gradient-to-r from-blue-500 to-blue-600 transition-all duration-300 ease-out"
                        style={{
                          width: `${(formData.required_capacity / selectedLocation.availableCapacity) * 100}%`,
                        }}
                      >
                        <div className="absolute inset-0 animate-pulse bg-white opacity-30"></div>
                      </div>
                    </div>
                    <div
                      className={`mt-2 text-xs text-blue-700 dark:text-blue-300 ${
                        language === "ar" ? "text-right" : "text-left"
                      }`}
                    >
                      {formData.required_capacity} {t("of")}{" "}
                      {selectedLocation.availableCapacity}{" "}
                      {t("available capacity")}
                    </div>
                  </div>
                )}

                {/* Validation Messages */}
                {formData.required_capacity >
                  selectedLocation.availableCapacity && (
                  <div
                    className={`mt-2 flex items-center text-sm text-red-600 dark:text-red-400 ${
                      language === "ar" ? "flex-row-reverse" : ""
                    }`}
                  >
                    <FaInfoCircle
                      className={`h-4 w-4 ${
                        language === "ar" ? "ml-2" : "mr-2"
                      }`}
                    />
                    {t("Required capacity exceeds available capacity")}
                  </div>
                )}

                {formData.required_capacity > 0 &&
                  formData.required_capacity <=
                    selectedLocation.availableCapacity && (
                    <div
                      className={`mt-2 flex items-center text-sm text-green-600 dark:text-green-400 ${
                        language === "ar" ? "flex-row-reverse" : ""
                      }`}
                    >
                      <FaCheck
                        className={`h-4 w-4 ${
                          language === "ar" ? "ml-2" : "mr-2"
                        }`}
                      />
                      {t("Capacity available for your reservation")}
                    </div>
                  )}
              </div>
            </div>
          ) : (
            <div className="flex h-64 items-center justify-center rounded-lg border-2 border-dashed border-gray-300 bg-gray-50 p-8 dark:border-gray-600 dark:bg-gray-800">
              <div className="text-center">
                <div className="mx-auto mb-4 h-16 w-16 rounded-full bg-gray-200 p-4 dark:bg-gray-700">
                  <FaInfoCircle className="h-8 w-8 text-gray-400" />
                </div>
                <h3 className="mb-2 text-lg font-medium text-gray-900 dark:text-white">
                  {t("Capacity Information")}
                </h3>
                <p className="text-sm text-gray-500 dark:text-gray-400">
                  {t("Select location and dates to view capacity")}
                </p>
                <div className="mt-4 text-xs text-gray-400 dark:text-gray-500">
                  {t("Real-time capacity data will be displayed here")}
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default ReservationFormClientLocation;
