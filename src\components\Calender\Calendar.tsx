"use client";

import React, { useState, useRef, useEffect, useMemo } from 'react';
import Breadcrumb from "../Breadcrumbs/Breadcrumb";
import useLanguage from "@/hooks/useLanguage";
import CalendarHeader from './CalendarHeader';
import CalendarBody from './CalendarBody';
import { getMonthDays, getYear } from '@/utils/dateUtils';
import { EventDetails } from "@/lib/interfaces/finaces";
import { ReservationDetails } from "@/lib/interfaces/ReservationDetails";
import IncomeServices from "@/lib/income";
import ExpensesServices from "@/lib/expenses";
import UpcomingEventsPage from '../Dashboard/UpcommingEventsPage/UpcommingEvents';

const Calendar = () => {
  const { t } = useLanguage();
  const [currentDate, setCurrentDate] = useState(new Date());
  const [viewType, setViewType] = useState('events');
  const [selectedDay, setSelectedDay] = useState<Date | null>(null);
  const [events, setEvents] = useState<EventDetails[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [reservations, setReservations] = useState<ReservationDetails[]>([]);
  const [selectedLocation, setSelectedLocation] = useState('');
  const eventsSectionRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const fetchEventsData = async () => {
      setIsLoading(true);
      setError(null);
      try {
        const incomeService = IncomeServices();
        const expensesService = ExpensesServices();
        
        // Fetch both income and expense events
        const [incomeEvents, expenseEvents] = await Promise.all([
          incomeService.getIncomes(),
          expensesService.getExpenses()
        ]);
        
        console.log("Fetched income events:", incomeEvents);
        console.log("Fetched expense events:", expenseEvents);
        
        // Combine and set the events
        const allEvents = [...incomeEvents, ...expenseEvents];
        setEvents(allEvents);
      } catch (err) {
        console.error("Error fetching events data:", err);
        setError("Failed to load events data. Please try again later.");
      } finally {
        setIsLoading(false);
      }
    };

    fetchEventsData();
  }, []);

  // Filter events by the selected month/year
  const filteredEvents = useMemo(() => {
    return events.filter(event => {
      if (!event.dueDate) return false;
      
      const eventDate = new Date(event.dueDate);
      return (
        eventDate.getFullYear() === currentDate.getFullYear() && 
        eventDate.getMonth() === currentDate.getMonth()
      );
    });
  }, [events, currentDate]);

  useEffect(() => {
    if (reservations.length > 0) {
      setSelectedLocation(reservations[0].location);
    }
  }, [reservations]);

  const handlePrevMonth = () => {
    setCurrentDate(new Date(currentDate.getFullYear(), currentDate.getMonth() - 1, 1));
  };

  const handleNextMonth = () => {
    setCurrentDate(new Date(currentDate.getFullYear(), currentDate.getMonth() + 1, 1));
  };

  const handleYearChange = (year: number) => {
    setCurrentDate(new Date(year, currentDate.getMonth(), 1));
  };

  const handleDayClick = (day: number) => {
    setSelectedDay(new Date(currentDate.getFullYear(), currentDate.getMonth(), day));
    if (eventsSectionRef.current) {
      eventsSectionRef.current.scrollIntoView({ behavior: 'smooth' });
    }
  };

  const days = getMonthDays(currentDate);
  const year = getYear(currentDate);
  const month = currentDate.getMonth();

  const selectedDayEvents = selectedDay
    ? filteredEvents.filter(event => {
        const eventDate = new Date(event.dueDate);
        return eventDate.toDateString() === selectedDay.toDateString();
      })
    : [];

  return (
    <div className="mx-auto max-w-7xl">
      <Breadcrumb pageName={t("calendar")} />

      {isLoading ? (
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
        </div>
      ) : error ? (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative" role="alert">
          <strong className="font-bold">{t("Error!")}</strong>
          <span className="block sm:inline"> {t(error)}</span>
        </div>
      ) : (
        <>
          <div className="w-full max-w-full rounded-sm border border-stroke bg-white shadow-default dark:border-strokedark dark:bg-boxdark">
            <CalendarHeader
              currentDate={currentDate}
              onPrevMonth={handlePrevMonth}
              onNextMonth={handleNextMonth}
              onYearChange={handleYearChange}
            />
            <CalendarBody 
              days={days} 
              year={year} 
              month={month} 
              viewType={viewType} 
              events={filteredEvents} 
              onDayClick={handleDayClick} 
            />
          </div>
          {selectedDay && (
            <div className="mt-4" ref={eventsSectionRef}>
              <h2 className="text-xl font-semibold">
                {t("Events")} {t("of")} {selectedDay.toLocaleDateString()}
              </h2>
              {selectedDayEvents.length > 0 ? (
                <UpcomingEventsPage events={selectedDayEvents} setEvents={setEvents} />
              ) : (
                <div className="bg-gray-50 p-4 text-center rounded-lg mt-4">
                  <p className="text-gray-500">{t("No events scheduled for this day.")}</p>
                </div>
              )}
            </div>
          )}
        </>
      )}
    </div>
  );
};

export default Calendar;