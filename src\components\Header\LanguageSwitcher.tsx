import useLanguage from '@/hooks/useLanguage';
import { useEffect, useState } from 'react';

const LanguageSwitcher = () => {
  const { language, toggleLanguage } = useLanguage();
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  if (!mounted) return null;

  return (
    <div className="relative inline-block">
      <button
        onClick={toggleLanguage}
        className={`
          w-[80px] h-9 
          rounded-full 
          bg-gradient-to-r from-gray-100 to-gray-200 
          dark:from-gray-700 dark:to-gray-800 
          relative 
          transition-all duration-300 ease-in-out
          overflow-hidden
          shadow-inner
          hover:shadow-lg
          border border-gray-200 dark:border-gray-600
        `}
      >
        <div
          className={`
            absolute 
            top-1 
            ${language === 'en' ? 'left-1' : 'right-1'}
            w-7 h-7 
            bg-white dark:bg-gray-900
            rounded-full 
            transition-all duration-300 ease-in-out
            shadow-md
            hover:shadow-lg
            border-2 border-gray-100 dark:border-gray-700
          `}
        />
        <div className={`
          flex justify-between 
          px-3
          text-xs font-semibold
          h-full items-center
          ${language === 'en' ? 'flex-row' : 'flex-row-reverse'}
        `}>
          <span className={`transition-colors duration-300 ${language === 'en' ? 'text-black-400' : 'text-black-100'}`}>EN</span>
          <span className={`transition-colors duration-300 ${language === 'ar' ? 'text-black-400' : 'text-black-100'}`}>AR</span>
        </div>
      </button>
    </div>
  );
};

export default LanguageSwitcher;