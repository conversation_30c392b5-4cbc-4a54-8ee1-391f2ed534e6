"use client";
import "jsvectormap/dist/jsvectormap.css";
import "flatpickr/dist/flatpickr.min.css";
import "@/css/satoshi.css";
import "@/css/style.css";
import React, { useEffect, useState } from "react";
import { LoadingComp } from "@/components/common/Loading";
import useLanguage from "@/hooks/useLanguage";
import { AuthProvider } from "./context/AuthContext";
import { SessionProvider } from "next-auth/react";
import { usePathname, useRouter } from "next/navigation";
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';

const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 5 * 60 * 1000, 
      retry: 2,
    },
  },
});

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [loading, setLoading] = useState<boolean>(true);
  const { language, toggleLanguage } = useLanguage();
  const [langDirection, setLangDirection] = useState<"ltr" | "rtl">(language === "ar" ? "rtl" : "ltr");

  const pathname = usePathname();
  const router = useRouter();

  useEffect(() => {
    setTimeout(() => setLoading(false), 1000);
    setLangDirection(language === "ar" ? "rtl" : "ltr");
  }, [language]);

  return (
    <html lang={language} dir={langDirection}>
      <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" />
      <body suppressHydrationWarning={true}>
        <div className="dark:bg-boxdark-2 dark:text-bodydark">
          {loading ? (
            <div className="fixed top-0 left-0 z-9999 flex h-screen w-full items-center justify-center bg-black dark:bg-boxdark">
              <LoadingComp />
            </div>
          ) : (
            <QueryClientProvider client={queryClient}>
              <SessionProvider>
                <AuthProvider>
                  {children}
                </AuthProvider>
              </SessionProvider>
            </QueryClientProvider>
          )}
        </div>
      </body>
    </html>
  );
}