import { Reservation } from "./reservation";
export interface Contact {
  id: string;
  name: string;
  email: string;
  phone?: string;
  percentage?: number;
  amount?: number;
}

export interface Location {
  id: string;
  name: string;
  address?: string;
  city?: string;
  capacity?: number;
  totalCapacity?: number;
  takenCapacity?: number;
  country?: string;
  postalCode?: string; 
  percentage?: number;
  amount?: number;
  areWeOwners?: boolean;
}

export interface EditingHistory {
  editedBy: string;
  editedAt: string;
  changes: Record<string, any>;
}


export interface EventDetails {
  id: string;
  category: "income" | "expense" | string;
  type: string;
  title: string;
  amount: number;
  dueDate: string;
  description?: string;
  status: "completed" | "pending" | "cancelled" | "upcoming" | "overdue";
  priority: "low" | "medium" | "high";
  contact?: Contact;
  location?: Location;
  lastEdited: string;
  received_date?: string; // Date when the event was received
  paid_date?: string; // Date when the event was paid
  lastEditedBy: string;
  editingHistory?: EditingHistory[];
  child_count?: number;
  child_events?: EventDetails[];
  percentage?: number;
  notificationDate?: string;
  reservation_id?: Reservation["id"];
  contract_id?: string;
  actual_amount? : number;
  paidTo?: string;
  paymentStatus?: string;
  frequency?: string;
  created_at?: string; // Date when the event was created
  income_id?: string; // For expenses linked to specific income events
  is_deleted?: boolean; // For soft delete functionality

  // Other possible properties based on component usage
  eventBy?: string;
  isPaid?: boolean;
  isRecurring?: boolean;
  vendor?: string;
}