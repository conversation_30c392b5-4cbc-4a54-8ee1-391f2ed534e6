import React, { useState, useEffect } from "react";
import { Reservation } from "@/lib/interfaces/reservation";
import useLanguage from "@/hooks/useLanguage";
import {
  FaEye,
  FaTrashAlt,
  FaFilter,
  FaTimes,
  FaSearch,
  FaCalendarAlt,
  FaMapMarkerAlt,
  FaUser,
  FaMoneyBillAlt,
} from "react-icons/fa";
import { useReservations } from "@/hooks/useReservations";
import { LoadingComp } from "@/components/common/Loading";

interface ReservationListProps {
  props_reservations?: Reservation[];
  onReservationClick?: (id: number, reservation?: Reservation) => void;
  onDeleteReservation: (id: number) => void;
}

const ReservationList: React.FC<ReservationListProps> = ({
  props_reservations,
  onReservationClick,
  onDeleteReservation,
}) => {
  const { t, language } = useLanguage();
  const [reservations, setReservations] = useState<Reservation[]>([]);
  const [searchTerm, setSearchTerm] = useState("");
  const [showActualAmount, setShowActualAmount] = useState(false);

  const [statusFilter, setStatusFilter] = useState<string>("all");
  const [dateFilter, setDateFilter] = useState<{ start: string; end: string }>({
    start: "",
    end: "",
  });
  const [isCardView, setIsCardView] = useState(window.innerWidth < 768);
  const [showFilters, setShowFilters] = useState(false);

  const { data, isLoading, isError } = useReservations();

  useEffect(() => {
    if (props_reservations && props_reservations.length > 0) {
      console.log("Using provided reservations data:", props_reservations);
      setReservations(
        (props_reservations as any[]).map((item) => ({
          id: item.id,
          title: item.title || "",
          description: item.description || "",
          total_amount: item.total_amount || 0,
          start_date: item.start_date || "",
          end_date: item.end_date || "",
          status: item.status || "pending",
          contact: item.contact || { name: "", email: "", phone: "" },
          location: item.location || {
            name: "",
            address: "",
            city: "",
            country: "",
          },
          actual_amount: item.actual_amount || 0,
        })) as Reservation[],
      );
    } else if (data?.reservations) {
      console.log("Fetched reservations data:", data);

      setReservations(
        (data.reservations as any[]).map((item) => ({
          id: item.id,
          title: item.title || "",
          description: item.description || "",
          total_amount: item.total_amount || 0,
          start_date: item.start_date || "",
          end_date: item.end_date || "",
          status: item.status || "pending",
          contact: item.contact || { name: "", email: "", phone: "" },
          location: item.location || {
            name: "",
            address: "",
            city: "",
            country: "",
          },
          actual_amount: item.actual_amount || 0,
        })) as Reservation[],
      );
    }
  }, [props_reservations, data]);

  // Listen for window resizes to toggle between card and table view
  useEffect(() => {
    const handleResize = () => {
      setIsCardView(window.innerWidth < 768);
    };

    window.addEventListener("resize", handleResize);
    return () => window.removeEventListener("resize", handleResize);
  }, []);

  // Function to calculate status based on dates
  const calculateStatus = (startDate: string, endDate: string): string => {
    const now = new Date();
    const start = new Date(startDate);
    const end = new Date(endDate);

    // Reset time to compare only dates
    now.setHours(0, 0, 0, 0);
    start.setHours(0, 0, 0, 0);
    end.setHours(0, 0, 0, 0);

    if (now < start) {
      return t("pending");
    } else if (now > end) {
      return t("completed");
    } else {
      return t("active");
    }
  };

  // Apply filters to reservations
  const filteredReservations = reservations.filter((reservation) => {
    const calculatedStatus = calculateStatus(
      reservation.start_date,
      reservation.end_date,
    );

    const matchesSearch =
      reservation.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      reservation.contact.name
        .toLowerCase()
        .includes(searchTerm.toLowerCase()) ||
      reservation.location.name
        .toLowerCase()
        .includes(searchTerm.toLowerCase());

    const matchesStatus =
      statusFilter === "all" || calculatedStatus === statusFilter;

    const startDate = dateFilter.start ? new Date(dateFilter.start) : null;
    const endDate = dateFilter.end ? new Date(dateFilter.end) : null;
    const reservationStart = new Date(reservation.start_date);
    const reservationEnd = new Date(reservation.end_date);

    const matchesDate =
      (!startDate ||
        reservationStart >= startDate ||
        reservationEnd >= startDate) &&
      (!endDate || reservationStart <= endDate);

    return matchesSearch && matchesStatus && matchesDate;
  });

  const resetFilters = () => {
    setSearchTerm("");
    setStatusFilter("all");
    setDateFilter({ start: "", end: "" });
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString(
      language === "ar" ? "ar-EG" : "en-US",
    );
  };

  const getStatusBadgeClass = (status: string) => {
    switch (status) {
      case t("active"):
        return "bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-100";
      case t("pending"):
        return "bg-yellow-100 text-yellow-800 dark:bg-yellow-800 dark:text-yellow-100";
      case t("completed"):
        return "bg-blue-100 text-blue-800 dark:bg-blue-800 dark:text-blue-100";
      case t("cancelled"):
        return "bg-red-100 text-red-800 dark:bg-red-800 dark:text-red-100";
      default:
        return "bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-100";
    }
  };

  const getReservationDuration = (start_date: string, end_date: string) => {
    const start = new Date(start_date);
    const end = new Date(end_date);
    const months =
      (end.getFullYear() - start.getFullYear()) * 12 +
      (end.getMonth() - start.getMonth());
    return Math.max(0, months);
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat(language === "ar" ? "ar-EG" : "en-US", {
      style: "currency",
      currency: "EGP",
      minimumFractionDigits: 0,
    }).format(amount);
  };

  // Function to handle reservation click and pass the full reservation data
  const handleReservationClick = (id: string) => {
    const reservation = reservations.find((res) => res.id === id);
    // Convert string ID to number before passing to parent component
    if (onReservationClick) {
      onReservationClick(Number(id), reservation);
    }
  };

  if (isLoading) {
    return (
      <div className="flex h-60 items-center justify-center">
        <LoadingComp />
      </div>
    );
  }

  if (isError) {
    return (
      <div className="rounded-lg bg-white p-4 shadow dark:bg-gray-800 sm:p-6">
        <div className="py-8 text-center text-red-500 dark:text-red-400">
          {t("Error loading reservations")}
        </div>
      </div>
    );
  }

  return (
    <div className="w-full max-w-full overflow-x-hidden rounded-lg bg-white shadow dark:bg-gray-800">
      {/* Mobile Filter Toggle */}
      <div className="border-b border-gray-200 p-4 dark:border-gray-700 lg:hidden">
        <button
          onClick={() => setShowFilters(!showFilters)}
          className="flex w-full items-center justify-between rounded-lg bg-gray-100 px-4 py-2 dark:bg-gray-700"
        >
          <div className="flex items-center">
            <FaFilter className="mr-2 text-gray-600 dark:text-gray-300" />
            <span className="font-medium text-gray-700 dark:text-gray-200">
              {t("Filters")}
              {(searchTerm ||
                statusFilter !== "all" ||
                dateFilter.start ||
                dateFilter.end) && (
                <span className="ml-2 rounded-full bg-blue-500 px-2 py-0.5 text-xs text-white">
                  {t("Active")}
                </span>
              )}
            </span>
          </div>
          <span className="text-gray-500">{showFilters ? "−" : "+"}</span>
        </button>
      </div>

      {/* Filters - Collapsible on mobile, always visible on desktop */}
      <div
        className={`${isCardView && !showFilters ? "hidden" : "block"} border-b border-gray-200 bg-gray-50 p-4 dark:border-gray-700 dark:bg-gray-800/60 sm:p-6`}
      >
        {/* Search - Full width on all screens */}
        <div className="mb-4 w-full">
          <div className="relative">
            <input
              type="text"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              placeholder={t("Search reservations...")}
              className="w-full rounded-lg border py-2.5 pl-10 pr-10 dark:border-gray-600 dark:bg-gray-700 dark:text-gray-200"
            />
            <FaSearch className="absolute left-3 top-3 text-gray-400" />
            {searchTerm && (
              <button
                onClick={() => setSearchTerm("")}
                className="absolute right-3 top-2.5 text-gray-400 hover:text-gray-600"
              >
                <FaTimes />
              </button>
            )}
          </div>
        </div>

        {/* Filter Controls - Responsive grid layout */}
        <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-4">
          {/* Status Filter */}
          <div>
            <label
              htmlFor="statusFilter"
              className="mb-1 block text-sm font-medium text-gray-700 dark:text-gray-300"
            >
              {t("Status")}
            </label>
            <select
              id="statusFilter"
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
              className="w-full rounded-lg border px-3 py-2 dark:border-gray-600 dark:bg-gray-700 dark:text-gray-200"
            >
              <option value="all">{t("All Statuses")}</option>
              <option value="active">{t("Active")}</option>
              <option value="pending">{t("Pending")}</option>
              <option value="completed">{t("Completed")}</option>
              <option value="cancelled">{t("Cancelled")}</option>
            </select>
          </div>

          {/* Start Date */}
          <div>
            <label
              htmlFor="startDate"
              className="mb-1 block text-sm font-medium text-gray-700 dark:text-gray-300"
            >
              {t("Start Date")}
            </label>
            <div className="relative">
              <FaCalendarAlt className="absolute left-3 top-2.5 text-gray-400" />
              <input
                id="startDate"
                type="text"
                placeholder="dd/mm/yyyy"
                value={dateFilter.start || ""}
                onChange={(e) => {
                  const value = e.target.value;
                  // Optional: Add basic validation (e.g., using regex)
                  const isValidFormat = /^\d{2}\/\d{2}\/\d{4}$/.test(value);
                  if (!value || isValidFormat) {
                    setDateFilter({ ...dateFilter, start: value });
                  }
                }}
                className="w-full rounded-lg border py-2 pl-10 dark:border-gray-600 dark:bg-gray-700 dark:text-gray-200"
              />
            </div>
          </div>

          {/* End Date */}
          <div>
            <label
              htmlFor="endDate"
              className="mb-1 block text-sm font-medium text-gray-700 dark:text-gray-300"
            >
              {t("End Date")}
            </label>
            <div className="relative">
              <FaCalendarAlt className="absolute left-3 top-2.5 text-gray-400" />
              <input
                id="endDate"
                type="text"
                placeholder="dd/mm/yyyy"
                value={dateFilter.end || ""}
                onChange={(e) => {
                  const value = e.target.value;
                  const isValidFormat = /^\d{2}\/\d{2}\/\d{4}$/.test(value);
                  if (!value || isValidFormat) {
                    setDateFilter({ ...dateFilter, end: value });
                  }
                }}
                className="w-full rounded-lg border py-2 pl-10 dark:border-gray-600 dark:bg-gray-700 dark:text-gray-200"
              />
            </div>
          </div>

          {/* Reset Button - Aligned with inputs */}
          <div className="flex items-end">
            <button
              onClick={resetFilters}
              className="flex w-full items-center justify-center gap-2 rounded-lg bg-red-500 px-4 py-2 text-white transition-colors hover:bg-red-600"
              aria-label={t("Reset Filters")}
            >
              <FaTimes size={14} />
              <span>{t("Reset Filters")}</span>
            </button>
          </div>
        </div>
      </div>

      {/* Results Count */}
      <div className="flex flex-col gap-4 border-b border-gray-200 bg-white px-4 py-4 dark:border-gray-700 dark:bg-gray-800 sm:flex-row sm:items-center sm:justify-between sm:px-6">
        {/* Description */}
        <div>
          <p className="text-sm text-gray-600 dark:text-gray-300">
            {t("Showing")}{" "}
            <span className="font-semibold">{filteredReservations.length}</span>{" "}
            {t("of")}{" "}
            <span className="font-semibold">{reservations.length}</span>{" "}
            {t("reservations")}
          </p>
          <p className="text-xs text-gray-500 dark:text-gray-400">
            {t("Use the toggles to customize the view and displayed data.")}
          </p>
        </div>

        {/* Toggle Controls */}
        <div className="flex flex-col items-start gap-4 sm:flex-row sm:items-center">
          {/* Toggle for Actual Amount */}
          <label className="flex cursor-pointer items-center gap-2">
            <span className="text-sm text-gray-700 dark:text-gray-300">
              {language === "ar" ? t("Our Amount") : t("Total Amount")}
            </span>
            <div className="relative h-5 w-10 rounded-full bg-gray-300">
              <input
                type="checkbox"
                className="sr-only"
                checked={showActualAmount}
                onChange={() => setShowActualAmount(!showActualAmount)}
                aria-label={t("Toggle Actual/Total Amount")}
              />
              <div
                className={`absolute left-0.5 top-0.5 h-4 w-4 transform rounded-full bg-white shadow transition-transform ${
                  showActualAmount ? "translate-x-5" : ""
                }`}
              />
            </div>
            <span className="text-sm text-gray-700 dark:text-gray-300">
              {language === "ar" ? t("Total Amount") : t("Our Amount")}
            </span>
          </label>

          {/* Toggle for View Type (medium screens only) */}
          {typeof window !== "undefined" &&
            window.innerWidth >= 640 &&
            window.innerWidth < 1024 && (
              <label className="flex cursor-pointer items-center gap-2">
                <div className="relative h-5 w-10 rounded-full bg-gray-300">
                  <input
                    type="checkbox"
                    className="sr-only"
                    checked={isCardView}
                    onChange={() => setIsCardView(!isCardView)}
                    aria-label={t("Toggle Card/Table View")}
                  />
                  <div
                    className={`absolute left-0.5 top-0.5 h-4 w-4 transform rounded-full bg-white shadow transition-transform ${
                      isCardView ? "translate-x-5" : ""
                    }`}
                  />
                </div>
                <span className="text-sm text-gray-700 dark:text-gray-300">
                  {t("Card View")}
                </span>
              </label>
            )}
        </div>
      </div>

      {/* Card View for Mobile and optionally Tablet */}
      {isCardView ? (
        <div className="divide-y divide-gray-200 dark:divide-gray-700">
          {filteredReservations.length > 0 ? (
            filteredReservations.map((reservation) => {
              const calculatedStatus = calculateStatus(
                reservation.start_date,
                reservation.end_date,
              );
              return (
                <div
                  key={reservation.id}
                  className="dark:hover:bg-gray-750 p-4 transition-colors hover:bg-gray-50"
                  onClick={() => handleReservationClick(reservation.id)}
                >
                  <div className="mb-3 flex items-start justify-between">
                    <h3 className="truncate pr-2 text-base font-medium text-gray-900 dark:text-white">
                      {reservation.title}
                    </h3>
                    <span
                      className={`flex-shrink-0 rounded-full px-2.5 py-1 text-xs font-medium leading-5 ${getStatusBadgeClass(calculatedStatus)}`}
                    >
                      {calculatedStatus.charAt(0).toUpperCase() +
                        calculatedStatus.slice(1)}
                    </span>
                  </div>

                  <div className="grid grid-cols-1 gap-2 text-sm sm:grid-cols-2">
                    <div className="flex items-center text-gray-500 dark:text-gray-400">
                      <FaUser className="mr-2 flex-shrink-0" size={14} />
                      <span className="truncate">
                        {reservation.contact.name}
                      </span>
                    </div>
                    <div className="flex items-center text-gray-500 dark:text-gray-400">
                      <FaMapMarkerAlt
                        className="mr-2 flex-shrink-0"
                        size={14}
                      />
                      <span className="truncate">
                        {reservation.location.name}
                      </span>
                    </div>
                    <div className="flex items-center text-gray-500 dark:text-gray-400">
                      <FaCalendarAlt className="mr-2 flex-shrink-0" size={14} />

                      <span className="text-xs">
                        (
                        {Number(
                          getReservationDuration(
                            reservation.start_date,
                            reservation.end_date,
                          ),
                        ).toLocaleString(
                          language === "ar" ? "ar-EG" : "en-US",
                        )}{" "}
                        {t("months")})
                      </span>
                    </div>
                    <div className="flex items-center font-medium text-gray-900 dark:text-white">
                      <FaMoneyBillAlt
                        className="mr-2 flex-shrink-0"
                        size={14}
                      />
                      <span className="truncate">
                        ${reservation.total_amount.toLocaleString()}
                      </span>
                    </div>
                  </div>
                </div>
              );
            })
          ) : (
            <div className="px-4 py-10 text-center">
              <p className="text-gray-500 dark:text-gray-400">
                {t("No reservations found")}
              </p>
              <p className="mt-2 text-sm text-gray-400 dark:text-gray-500">
                {t("Try adjusting your filters")}
              </p>
            </div>
          )}
        </div>
      ) : (
        /* Table View for Desktop */
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
            <thead className="bg-gray-50 dark:bg-gray-700">
              <tr>
                <th
                  className={`px-6 py-3 text-xs font-medium uppercase tracking-wider text-gray-500 dark:text-gray-300 ${language === "ar" ? "text-right" : "text-left"}`}
                >
                  {t("Title")}
                </th>
                <th
                  className={`px-6 py-3 text-xs font-medium uppercase tracking-wider text-gray-500 dark:text-gray-300 ${language === "ar" ? "text-right" : "text-left"}`}
                >
                  {t("Client")}
                </th>
                <th
                  className={`px-6 py-3 text-xs font-medium uppercase tracking-wider text-gray-500 dark:text-gray-300 ${language === "ar" ? "text-right" : "text-left"}`}
                >
                  {t("Location")}
                </th>
                <th
                  className={`px-6 py-3 text-xs font-medium uppercase tracking-wider text-gray-500 dark:text-gray-300 ${language === "ar" ? "text-right" : "text-left"}`}
                >
                  {t("Period")}
                </th>
                <th
                  className={`px-6 py-3 text-xs font-medium uppercase tracking-wider text-gray-500 dark:text-gray-300 ${language === "ar" ? "text-right" : "text-left"}`}
                >
                  {t("Amount")}
                </th>
                <th
                  className={`px-6 py-3 text-xs font-medium uppercase tracking-wider text-gray-500 dark:text-gray-300 ${language === "ar" ? "text-right" : "text-left"}`}
                >
                  {t("Status")}
                </th>
              </tr>
            </thead>
            <tbody className="divide-y divide-gray-200 bg-white dark:divide-gray-700 dark:bg-gray-800">
              {filteredReservations.length > 0 ? (
                filteredReservations.map((reservation) => {
                  const calculatedStatus = calculateStatus(
                    reservation.start_date,
                    reservation.end_date,
                  );
                  return (
                    <tr
                      key={reservation.id}
                      className="dark:hover:bg-gray-750 cursor-pointer transition-colors hover:bg-gray-50"
                      onClick={() => handleReservationClick(reservation.id)}
                    >
                      <td className="whitespace-nowrap px-6 py-4">
                        <div className="text-sm font-medium text-gray-900 dark:text-white">
                          {reservation.title}
                        </div>
                      </td>
                      <td className="whitespace-nowrap px-6 py-4">
                        <div className="text-sm text-gray-500 dark:text-gray-400">
                          {reservation.contact.name}
                        </div>
                      </td>
                      <td className="whitespace-nowrap px-6 py-4">
                        <div className="text-sm text-gray-500 dark:text-gray-400">
                          {reservation.location.name}
                        </div>
                      </td>
                      <td className="whitespace-nowrap px-6 py-4">
                        <div className="text-sm text-gray-500 dark:text-gray-400">
                          <span className="mr-1">
                            {formatDate(reservation.start_date)} -{" "}
                            {formatDate(reservation.end_date)}
                          </span>
                          <span className="text-xs">
                            (
                            {Number(
                              getReservationDuration(
                                reservation.start_date,
                                reservation.end_date,
                              ),
                            ).toLocaleString(
                              language === "ar" ? "ar-EG" : "en-US",
                            )}{" "}
                            {t("months")})
                          </span>
                        </div>
                      </td>
                      <td className="whitespace-nowrap px-6 py-4">
                        <div className="text-sm font-medium text-gray-900 dark:text-white">
                          {showActualAmount
                            ? formatCurrency(reservation.actual_amount || 0)
                            : formatCurrency(reservation.total_amount)}
                        </div>
                      </td>
                      <td className="whitespace-nowrap px-6 py-4">
                        <span
                          className={`inline-flex rounded-full px-2.5 py-1 text-xs font-medium leading-5 ${getStatusBadgeClass(calculatedStatus)}`}
                        >
                          {calculatedStatus.charAt(0).toUpperCase() +
                            calculatedStatus.slice(1)}
                        </span>
                      </td>
                    </tr>
                  );
                })
              ) : (
                <tr>
                  <td colSpan={7} className="px-6 py-10 text-center">
                    <p className="text-gray-500 dark:text-gray-400">
                      {t("No reservations found")}
                    </p>
                    <p className="mt-2 text-sm text-gray-400 dark:text-gray-500">
                      {t("Try adjusting your filters")}
                    </p>
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>
      )}
    </div>
  );
};

export default ReservationList;
