import React, { useState } from 'react';
import useLanguage from "@/hooks/useLanguage"; // Make sure this import exists

interface CalendarHeaderProps {
  currentDate: Date;
  onPrevMonth: () => void;
  onNextMonth: () => void;
  onYearChange: (year: number) => void;
}

const CalendarHeader: React.FC<CalendarHeaderProps> = ({ currentDate, onPrevMonth, onNextMonth, onYearChange }) => {
  const { t, language } = useLanguage(); // Make sure lang or direction is available

  const isRTL = language === "ar"; // Adjust if your hook uses a different property

  const monthNames = [
    t("January"),
    t("February"),
    t("March"),
    t("April"),
    t("May"),
    t("June"),
    t("July"),
    t("August"),
    t("September"),
    t("October"),
    t("November"),
    t("December"),
  ];
  const month = currentDate.getMonth();
  const year = currentDate.getFullYear();
  const [isMonthDropdownOpen, setIsMonthDropdownOpen] = useState(false);

  const handleYearChange = (event: React.ChangeEvent<HTMLSelectElement>) => {
    onYearChange(parseInt(event.target.value, 10));
  };

  const handleMonthChange = (monthIndex: number) => {
    const newDate = new Date(currentDate);
    newDate.setMonth(monthIndex);
    const monthsDiff = monthIndex - month;
    if (monthsDiff > 0) {
      for (let i = 0; i < monthsDiff; i++) {
        onNextMonth();
      }
    } else if (monthsDiff < 0) {
      for (let i = 0; i < Math.abs(monthsDiff); i++) {
        onPrevMonth();
      }
    }
    setIsMonthDropdownOpen(false);
  };

  const years = Array.from({ length: 50 }, (_, i) => year - 25 + i);

  return (
    <div className="flex justify-center items-center p-4 border-b border-gray-200 bg-white shadow-sm">
      <div className="flex items-center space-x-6">
        <button
          onClick={isRTL ? onNextMonth : onPrevMonth}
          className="px-3 py-1 text-gray-700 hover:text-black hover:bg-gray-100 rounded-full transition duration-200 ease-in-out"
          aria-label={t("Previous month")}
        >
          {/* Right arrow for RTL, left arrow for LTR */}
          {isRTL ? (
            // Right arrow
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4-4a1 1 0 01-1.414 0z" clipRule="evenodd" />
            </svg>
          ) : (
            // Left arrow
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" clipRule="evenodd" />
            </svg>
          )}
        </button>
        
        <div className="flex items-center space-x-3">
          {/* Month Dropdown */}
          <div className="relative">
            <button
              onClick={() => setIsMonthDropdownOpen(!isMonthDropdownOpen)}
              className="px-4 py-2 text-gray-800 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 flex items-center space-x-1"
            >
              <span className="font-medium">{monthNames[month]}</span>
              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
              </svg>
            </button>
            
            {isMonthDropdownOpen && (
              <div className="absolute z-10 mt-1 w-40 bg-white border border-gray-300 rounded-md shadow-lg py-1 max-h-60 overflow-auto">
                {monthNames.map((monthName, idx) => (
                  <button
                    key={idx}
                    onClick={() => handleMonthChange(idx)}
                    className={`w-full text-left px-4 py-2 hover:bg-gray-100 ${idx === month ? 'bg-blue-100 text-blue-700 font-medium' : 'text-gray-700'}`}
                  >
                    {monthName}
                  </button>
                ))}
              </div>
            )}
          </div>
          
          {/* Year Dropdown */}
          <label htmlFor="calendar-year-select" className="sr-only">{t("Select year")}</label>
          <select
            id="calendar-year-select"
            value={year}
            onChange={handleYearChange}
            className="px-3 py-2 text-gray-800 bg-white border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 transition duration-200 ease-in-out"
            aria-label={t("Select year")}
          >
            {years.map((yr) => (
              <option key={yr} value={yr}>
                {t("Year")} {yr}
              </option>
            ))}
          </select>
        </div>
        
        <button
          onClick={isRTL ? onPrevMonth : onNextMonth}
          className="px-3 py-1 text-gray-700 hover:text-black hover:bg-gray-100 rounded-full transition duration-200 ease-in-out"
          aria-label={t("Next month")}
        >
          {/* Left arrow for RTL, right arrow for LTR */}
          {isRTL ? (
            // Left arrow
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" clipRule="evenodd" />
            </svg>
          ) : (
            // Right arrow
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4-4a1 1 0 01-1.414 0z" clipRule="evenodd" />
            </svg>
          )}
        </button>
      </div>
    </div>
  );
};

export default CalendarHeader;