"use client";
import React, { useState, useEffect } from "react";
import Breadcrumb from "@/components/Breadcrumbs/Breadcrumb";
import useLanguage from "@/hooks/useLanguage";
import { EventDetails } from "@/lib/interfaces/finaces";
import UpcomingEventPopup from "../Financials/common/UpcommingEventPopup.tsx";
import AnalyticsSection from "./Sections/AnalyticsSection";
import UpcomingEventsSection from "./Sections/UpcomingEventsSection";
import IncomeVsExpenseSection from "./Sections/IncomeVsExpenseSection";
import ExpectedVsActualSection from "./Sections/ExpectedVsActualSection";
import Analytics from "../Financials/Overview/Sections/Analytic";
import getCombinedEvents from "@/lib/events";
import { LoadingComp } from "@/components/common/Loading";
import { is } from "date-fns/locale";

const Dashboard: React.FC = () => {
    const [upcomingEvents, setUpcomingEvents] = useState<EventDetails[]>([]);
    const [upcomingEventsSectionData, setUpcomingEventsSectionData] = useState<EventDetails[]>([]);
    const { t } = useLanguage();
    const [selectedEvent, setSelectedEvent] = useState<EventDetails | null>(null);
    const [filterStatus, setFilterStatus] = useState("all");
    const [filterPriority, setFilterPriority] = useState("all");
    const [loading, setLoading] = useState<boolean>(true); // Loading state
    useEffect(() => {
        const today = new Date();
        const filteredEvents = upcomingEvents.filter(event => new Date(event.dueDate) >= today);
        setUpcomingEventsSectionData(filteredEvents.slice(0, 10));
    }, [upcomingEvents]);

    // sort upcoming events by date
    upcomingEvents.sort((a, b) => new Date(a.dueDate).getTime() - new Date(b.dueDate).getTime());


    const handlePopupClose = () => setSelectedEvent(null);
    const handleSave = (updatedEvent: EventDetails) => {
        setUpcomingEvents((prevEvents) =>
            prevEvents.map((event) => (event.id === updatedEvent.id ? updatedEvent : event))
        );
        handlePopupClose();
    };

    useEffect(() => {
        const fetchEvents = async () => {
          setLoading(true);
          try {
            const events = await getCombinedEvents();
            setUpcomingEvents(events);
          } catch (error) {
            console.error("Error fetching events:", error);
          } finally {
            setLoading(false);
          }
        };
    
        fetchEvents();
      }, []);

      
    return (
        loading ? (
            <div className="flex justify-center items-center h-screen">
                <LoadingComp />
            </div>
        ) : (

        <>
            <Breadcrumb pageName={t("overview")} />

            <Analytics events={upcomingEvents} />
            <div className="flex flex-col lg:flex-row gap-6">
                <div className="lg:w-2/3">
                    <AnalyticsSection events={upcomingEvents} />
                </div>

                <div className="lg:w-1/3">
                    <UpcomingEventsSection
                        onSave={handleSave}
                        events={upcomingEventsSectionData}
                        filterStatus={filterStatus}
                        filterPriority={filterPriority}
                        onFilterStatusChange={setFilterStatus}
                        onFilterPriorityChange={setFilterPriority}
                    />
                </div>
            </div>

            <div className="flex flex-col gap-6 mt-6">
                <div className="w-full">
                    <IncomeVsExpenseSection events={upcomingEvents} />
                </div>

                <div className="w-full">
                    <ExpectedVsActualSection events={upcomingEvents} />
                </div>
            </div>

            {selectedEvent && (
                <UpcomingEventPopup event={selectedEvent} onClose={handlePopupClose} onSave={handleSave} />
            )}
        </>
        )
    );
};

export default Dashboard;