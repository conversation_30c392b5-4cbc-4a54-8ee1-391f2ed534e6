import React from 'react';
import { EventDetails } from "@/lib/interfaces/finaces";
import useLanguage from "@/hooks/useLanguage";

interface CalendarDayProps {
  day: number;
  year: number;
  viewType: string;
  events: EventDetails[];
  onDayClick: (day: number) => void;
}

const CalendarDay: React.FC<CalendarDayProps> = ({ day, year, viewType, events, onDayClick }) => {
  // Group events by category for visual indicators
  const incomeEvents = events.filter(event => event.category === 'income');
  const expenseEvents = events.filter(event => event.category === 'expense');
  const { t } = useLanguage();
  const hasEvents = events.length > 0;
  const today = new Date();
  const isToday = today.getDate() === day && 
                  today.getMonth() === new Date(year, 0).getMonth() && 
                  today.getFullYear() === year;

  return (
    <td
      className={`ease relative h-20 cursor-pointer border border-stroke p-2 transition duration-500 hover:bg-gray-100 dark:border-strokedark dark:hover:bg-meta-4 md:h-25 md:p-6 xl:h-31 ${
        isToday ? 'bg-blue-50 dark:bg-blue-900/30' : ''
      }`}
      onClick={() => onDayClick(day)}
    >
      <span className={`font-medium ${isToday ? 'text-blue-600 dark:text-blue-400' : 'text-black dark:text-white'}`}>
        {day}
      </span>

      {viewType === 'events' && hasEvents && (
        <div className="mt-2 space-y-1">
          {incomeEvents.length > 0 && (
            <div className="flex items-center">
              <span className="w-2 h-2 rounded-full bg-green-500 mr-1"></span>
              <span className="text-xs text-gray-600 dark:text-gray-400">
                {incomeEvents.length} {t('income')}
              </span>
            </div>
          )}
          {expenseEvents.length > 0 && (
            <div className="flex items-center">
              <span className="w-2 h-2 rounded-full bg-red-500 mr-1"></span>
              <span className="text-xs text-gray-600 dark:text-gray-400">
                {expenseEvents.length} {t("expense")}
              </span>
            </div>
          )}
        </div>
      )}
    </td>
  );
};

export default CalendarDay;