"use client";

import React, { useState, useRef, useEffect } from "react";
import { <PERSON>a<PERSON><PERSON>, <PERSON>a<PERSON><PERSON><PERSON>, FaFilePdf } from "react-icons/fa";
import EventCard from "../../Financials/common/EventCard";
import useLanguage from "@/hooks/useLanguage";
import { EventDetails } from "@/lib/interfaces/finaces";
import FilterArea from "./FilterArea";
import SelectedEventDetails from "./SelectedEventDetails";
import AddEventPopup from "../../Financials/common/AddEventPopup.tsx/AddEventPopup";
import { generatePDF } from "./generatePDF";
import { usePermissions } from "@/hooks/usePermissions";
import { LoadingComp } from "@/components/common/Loading";
import EventWithChildrenCard from "@/components/Financials/common/EventWithChildrenCard";
import IncomeServices from "@/lib/income";
import ExpenseServices from "@/lib/expenses";

interface UpcomingEventsPageProps {
  events: EventDetails[];
  setEvents: React.Dispatch<React.SetStateAction<EventDetails[]>>;
  mode?: "overview" | "income" | "expenses";
  selectedEventId?: string; // Changed from number to string to match event.id type
  title?: string; // Optional title prop
}

const UpcomingEventsPage: React.FC<UpcomingEventsPageProps> = ({
  events,
  setEvents,
  mode = "overview",
  selectedEventId,
  title,
}) => {
  const [searchQuery, setSearchQuery] = useState("");
  const [filterType, setFilterType] = useState<"All" | "income" | "expense">(
    "All",
  );

  const [startDate, setStartDate] = useState("");
  const [endDate, setEndDate] = useState("");
  const [filterContact, setFilterContact] = useState("all");
  const [filterLocation, setFilterLocation] = useState("all");
  const [filterPriceFrom, setFilterPriceFrom] = useState("");
  const [filterPriceTo, setFilterPriceTo] = useState("");
  const [selectedEvent, setSelectedEvent] = useState<EventDetails | null>(null);
  const [isAddEventPopupOpen, setIsAddEventPopupOpen] = useState(false);
  const [isFilterOpen, setIsFilterOpen] = useState(false);
  const { t, language } = useLanguage();
  const infoSectionRef = useRef<HTMLDivElement>(null);
  const topRef = useRef<HTMLDivElement>(null);
  const { hasPermission, permissionsLoaded } = usePermissions();
  const incomeService = IncomeServices();
  const expenseService = ExpenseServices();
  const [createdAtStart, setCreatedAtStart] = useState("");
  const [createdAtEnd, setCreatedAtEnd] = useState("");
  const [filterStatus, setFilterStatus] = useState("All");
const [filterStatusOptions, setFilterStatusOptions] = useState<string[]>(["All"]);

  useEffect(() => {
    if (selectedEventId) {
      const eventToSelect = events.find(
        (event) => event.id.toString() === selectedEventId,
      );
      if (eventToSelect) {
        setSelectedEvent(eventToSelect);
        scrollToInfoSection();
      }
    }
  }, [selectedEventId, events]);

  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) =>
    setSearchQuery(e.target.value);

  const handleEdit = (id: number | string) => {
    // Convert id to string to ensure consistent comparison
    const eventToEdit = events.find(
      (event) => event.id.toString() === id.toString(),
    );
    if (eventToEdit) {
      setSelectedEvent(eventToEdit);
      scrollToInfoSection();
    }
  };
  const generateDescriptiveTitle = () => {
    let title =
      mode === "income"
        ? t("Income Report")
        : mode === "expenses"
          ? t("Expense Report")
          : t("Financial Report");

    if (filterContact !== "all") {
      title += ` ${t("for contact")}: "${filterContact}"`;
    }

    if (filterLocation !== "all") {
      title += ` ${t("at location")}: "${filterLocation}"`;
    }

    // Always include the date range
    if (startDate && endDate) {
      title += ` ${t("from")} ${new Date(startDate).toLocaleDateString(language === "ar" ? "ar-EG" : "en-US")} ${t("to")} ${new Date(endDate).toLocaleDateString(language === "ar" ? "ar-EG" : "en-US")}`;
    } else if (startDate) {
      title += ` ${t("starting from")} ${new Date(startDate).toLocaleDateString(language === "ar" ? "ar-EG" : "en-US")}`;
    } else if (endDate) {
      title += ` ${t("up to")} ${new Date(endDate).toLocaleDateString(language === "ar" ? "ar-EG" : "en-US")}`;
    } else {
      title += ` ${t("as of today")}`;
    }

    return title;
  };

  const handleDelete = (id: number | string) => {
    // Convert id to string for comparison
    setEvents(events.filter((event) => event.id.toString() !== id.toString()));
  };

  const handleHistory = (id: number | string) => {
    // Convert id to string for comparison
    const eventToViewHistory = events.find(
      (event) => event.id.toString() === id.toString(),
    );
    if (eventToViewHistory) {
      setSelectedEvent(eventToViewHistory);
    }
  };

  const handleSave = async (updatedEvent: EventDetails) => {

    if (updatedEvent.category === "income") {
      await incomeService.updateIncome(updatedEvent.id, {
        title: updatedEvent.title,
        amount: updatedEvent.amount,
        due_date: updatedEvent.dueDate,
        received_date: null,
        description: updatedEvent.description || "",
        status: updatedEvent.status,
        priority: updatedEvent.priority,
        // reservation_id removed as it is not part of CreateExpensePayload
        contact_id: updatedEvent.contact?.id || "",
        location_id: updatedEvent.location?.id || "",
        autoCreate: false,
        notification_date: null,
      });
    } else if (updatedEvent.category === "expense") {
      await expenseService.updateExpense(updatedEvent.id, {
        title: updatedEvent.title,
        amount: updatedEvent.amount,
        due_date: updatedEvent.dueDate,
        paid_date: null,
        description: updatedEvent.description || "",
        status: updatedEvent.status,
        priority: updatedEvent.priority,
        // reservation_id removed as it is not part of CreateExpensePayload
        contact_id: updatedEvent.contact?.id || "",
        location_id: updatedEvent.location?.id || "",
        autoCreate: false,
        notification_date: null,
      });
    }

    setSelectedEvent(null);
  };

  const handleAddEventSave = (newEvents: EventDetails[]) => {
    setEvents((prevEvents) => [...prevEvents, ...newEvents]);
  };

  const scrollToInfoSection = () => {
    if (infoSectionRef.current) {
      infoSectionRef.current.scrollIntoView({ behavior: "smooth" });
    }
  };

  const scrollToTop = () => {
    if (topRef.current) {
      topRef.current.scrollIntoView({ behavior: "smooth" });
    }
  };

  const clearFilters = () => {
    setFilterType("All");
    setFilterStatus("All");
    setStartDate("");
    setEndDate("");
    setFilterContact("all");
    setFilterLocation("all");
    setFilterPriceFrom("");
    setFilterPriceTo("");
    setSearchQuery("");
  };
  const filteredEvents = events
    .filter((event) => {
      const matchesSearch =
        event.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
        event.contact?.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        event.location?.name.toLowerCase().includes(searchQuery.toLowerCase());
      const matchesFilterType =
        filterType === "All" || event.category === filterType;
      const matchesStatus =
        filterStatus === "All" || event.status === filterStatus;
      const matchesStartDate =
        startDate === "" || new Date(event.dueDate) >= new Date(startDate);
    const matchCreatedAtStart =
        createdAtStart === "" ||
        new Date(event.created_at || event.dueDate) >= new Date(createdAtStart);
      const matchCreatedAtEnd =
        createdAtEnd === "" ||
        new Date(event.created_at || event.dueDate) <= new Date(createdAtEnd);
      const matchesEndDate =
        endDate === "" || new Date(event.dueDate) <= new Date(endDate);
      const matchesContact =
        filterContact === "all" || event.contact?.name === filterContact;
      const matchesLocation =
        filterLocation === "all" || event.location?.name === filterLocation;
      const matchesPriceFrom =
        filterPriceFrom === "" || event.amount >= parseFloat(filterPriceFrom);
      const matchesPriceTo =
        filterPriceTo === "" || event.amount <= parseFloat(filterPriceTo);

      return (
        matchesSearch &&
        matchesFilterType &&
        matchesStatus &&
        matchesStartDate &&
        matchesEndDate &&
        matchesContact &&
        matchesLocation &&
        matchesPriceFrom &&
        matchesPriceTo
      );
    })
    .filter((event) => {
      if (mode === "income") return event.category === "income";
      if (mode === "expenses") return event.category === "expense";
      return true;
    });

// Update options based on filtered events
useEffect(() => {
  const uniqueStatuses = Array.from(
    new Set(filteredEvents.map((event) => event.status))
  );
  setFilterStatusOptions((prevOptions) => {
    const newOptions = ["All", ...uniqueStatuses];
    if (JSON.stringify(prevOptions) !== JSON.stringify(newOptions)) {
      return newOptions;
    }
    return prevOptions;
  });
}, [filteredEvents]);

useEffect(() => {
  if (!filterStatusOptions.includes(filterStatus)) {
    setFilterStatus("All");
  }
}, [filterStatusOptions, filterStatus]);


  if (!permissionsLoaded) {
    return <LoadingComp />;
  }
  return (
    <div className="min-h-screen p-4 sm:p-6">
      <div ref={topRef}></div>
      {/* Header */}
      <div className="mb-6 flex flex-wrap items-center justify-between gap-4">
        <h1 className="text-2xl font-bold text-gray-800 dark:text-gray-100 sm:text-3xl">
          {title ||
            (mode === "overview"
              ? t("upcoming events")
              : mode === "income"
                ? t("income events")
                : mode === "expenses"
                  ? t("expense events")
                  : "")}
        </h1>
        <div className="flex gap-2">
          <button
            onClick={() => setIsFilterOpen(!isFilterOpen)}
            className="flex items-center gap-2 rounded-lg bg-gray-500 px-4 py-2 text-white shadow hover:bg-gray-600 dark:bg-gray-700 dark:hover:bg-gray-800"
          >
            <FaFilter />
            {t("filter")}
          </button>
          <button
            onClick={() => {
              const title = generateDescriptiveTitle();
              generatePDF(filteredEvents, language, t, title);
            }}
            className="flex items-center gap-2 rounded-lg bg-red-500 px-4 py-2 text-white shadow hover:bg-red-600 dark:bg-red-700 dark:hover:bg-red-800"
          >
            <FaFilePdf />
            {t("generate pdf")}
          </button>
          {(mode === "income" && hasPermission("income", "create")) ||
          (mode === "expenses" && hasPermission("expenses", "create")) ||
          (mode === "overview" && hasPermission("financials", "create")) ? (
            <button
              className="flex items-center gap-2 rounded-lg bg-green-500 px-4 py-2 text-white shadow hover:bg-green-600 dark:bg-green-700 dark:hover:bg-green-800"
              onClick={() => {
                console.log("Add Event button clicked");

                setIsAddEventPopupOpen(true);
              }}
            >
              <FaPlus />
              {t("add event")}
            </button>
          ) : null}
        </div>
      </div>

      {/* Selected Event Details */}
      {selectedEvent && (
        <div ref={infoSectionRef}>
          <SelectedEventDetails
            event={selectedEvent}
            onClose={() => setSelectedEvent(null)}
            onEdit={() => handleEdit(selectedEvent.id)}
            onDelete={() => handleDelete(selectedEvent.id)}
            onHistory={() => handleHistory(selectedEvent.id)}
            onSave={handleSave} // Pass the handleSave function to handle saving the edited event
          />
        </div>
      )}
      {isFilterOpen && (
        <FilterArea
          filterType={filterType}
          setFilterType={setFilterType}
          filterStatusOptions={filterStatusOptions}
          filterStatus={filterStatus}
          setFilterStatus={(value) => {
          setFilterStatus(value || "All")}}
          createdAtStart={createdAtStart ? new Date(createdAtStart) : null}
          setCreatedAtStart={(date) =>
            setCreatedAtStart(date ? date.toISOString().split("T")[0] : "")
          }
          createdAtEnd={createdAtEnd ? new Date(createdAtEnd) : null}
          setCreatedAtEnd={(date) =>
            setCreatedAtEnd(date ? date.toISOString().split("T")[0] : "")
          }
          searchQuery={searchQuery}
          handleSearch={handleSearch}
          startDate={startDate ? new Date(startDate) : null}
          setStartDate={(date) =>
            setStartDate(date ? date.toISOString().split("T")[0] : "")
          }
          endDate={endDate ? new Date(endDate) : null}
          setEndDate={(date) =>
            setEndDate(date ? date.toISOString().split("T")[0] : "")
          }
          clearFilters={clearFilters}
          filterContact={filterContact}
          setFilterContact={setFilterContact}
          filterLocation={filterLocation}
          setFilterLocation={setFilterLocation}
          filterPriceFrom={filterPriceFrom}
          setFilterPriceFrom={setFilterPriceFrom}
          filterPriceTo={filterPriceTo}
          setFilterPriceTo={setFilterPriceTo}
          contactOptions={events
            .filter((event) => event.contact?.name) // Filter out undefined names
            .map((event) => ({
              value: event.contact!.name,
              label: event.contact!.name,
            }))}
          locationOptions={events
            .filter((event) => event.location?.name) // Filter out undefined names
            .map((event) => ({
              value: event.location!.name,
              label: event.location!.name,
            }))}
        />
      )}
      {(mode === "income" && hasPermission("income", "view")) ||
      (mode === "expenses" && hasPermission("expenses", "view")) ||
      (mode === "overview" && hasPermission("financials", "view")) ? (
        <>
          {/* Table Header */}
          <div className="hidden grid-cols-2 gap-4 rounded-t-lg bg-gray-100 p-4 dark:bg-gray-700 sm:grid sm:grid-cols-3 md:grid-cols-6">
            <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
              {t("title")}
            </span>
            <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
              {t("date")}
            </span>
            <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
              {t("amount")}
            </span>
            <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
              {t("status")}
            </span>
            <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
              {t("priority")}
            </span>
            <span className="text-right text-sm font-medium text-gray-700 dark:text-gray-300">
              {t("actions")}
            </span>
          </div>

          {/* Event Cards */}
          <div id="events-content" className="space-y-4">
            {filteredEvents.map((event) => {
              if (event.child_count && event.child_count > 0) {
                return (
                  <EventWithChildrenCard
                    key={event.id}
                    event={event}
                    onEdit={() => handleEdit(event.id)}
                    onDelete={() => handleDelete(event.id)}
                    onClick={() => {
                      handleEdit(event.id);
                      scrollToTop();
                    }}
                  />
                );
              } else {
                return (
                  <EventCard
                    key={event.id}
                    event={event}
                    onEdit={() => handleEdit(event.id)}
                    onDelete={() => handleDelete(event.id)}
                    onClick={() => {
                      handleEdit(event.id);
                      scrollToTop();
                    }}
                  />
                );
              }
            })}
            {filteredEvents.length === 0 && (
              <p className="col-span-full text-center text-gray-600 dark:text-gray-300">
                {t("no events found")}
              </p>
            )}
          </div>
        </>
      ) : (
        <div className="flex h-64 items-center justify-center">
          <p className="text-gray-600 dark:text-gray-300">
            {t("no permission to add events")}
          </p>
        </div>
      )}

      {isAddEventPopupOpen && (
        <AddEventPopup
          onClose={() => setIsAddEventPopupOpen(false)}
          onSave={handleAddEventSave}
          defaultCategory={mode === "income" ? "income" : "expense"}
        />
      )}
    </div>
  );
};

export default UpcomingEventsPage;
