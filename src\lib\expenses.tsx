import { AxiosInstance } from "axios";
import { EventDetails } from "./interfaces/finaces";
import apiClient from "./api/apiClient";
import { update } from "@react-spring/web";
import { toast } from "react-hot-toast";

interface CreateExpensePayload {
  title: string;
  amount: number;
  due_date: string;
  paid_date: string | null;
  description: string;
  status: string;
  priority: string;
  reservation_id?: string;
  contact_id?: string;
  location_id?: string;
  notification_date: string | null;
  autoCreate: boolean;
  isTotalAmount?: boolean;
}

interface CreateParentExpensePayload {
  title: string;
  amount: number;
  description: string;
  due_date: string | null;
  notification_view_date: string | null;
  priority: string;
  type_id: string;
  parent?: {
    title: string;
    amount: number;
    description: string;
  };
  children?: {
    amount: number;
    due_date: string | null;
    notification_view_date: string | null;
  }[];
}

const ExpenseServices = () => ({
  getExpenses: async (signal?: AbortSignal): Promise<EventDetails[]> => {
    try {
      console.log("Making API call to fetch expenses...");
      const response = await apiClient.get(`/expenses/api/listall/`, {
        signal: signal,
      });
      console.log("Raw expenses API response:", response);

      if (!response.data) {
        console.error("Expenses API response has no data property");
        return [];
      }

      // Check for 'expenses' property first
      if (response.data.expenses) {
        console.log(
          "Found expenses property in response, count:",
          response.data.expenses.length,
        );
        return response.data.expenses as EventDetails[];
      }

      // If no expenses property exists, check if response.data is an array directly
      if (Array.isArray(response.data)) {
        console.log(
          "Response data is an array, returning directly, count:",
          response.data.length,
        );
        return response.data;
      }

      console.error(
        "Could not find expenses data in the response:",
        response.data,
      );
      return [];
    } catch (error) {
      // Check if this is an abort error
      if (
        (error as any).name === "AbortError" ||
        (error as any).code === "ECONNABORTED"
      ) {
        console.log("Expenses API request was cancelled");
        return [];
      }
      console.error("Error fetching expenses:", error);
      throw error;
    }
  },

  // New function to get expenses by location ID
  getExpensesByLocation: async (
    locationId: string | number,
    signal?: AbortSignal,
  ): Promise<EventDetails[]> => {
    try {
      console.log(
        `Making API call to fetch expenses for location ID: ${locationId}...`,
      );
      const response = await apiClient.post(
        "/expenses/api/by-location/",
        { location_id: locationId },
        { signal: signal },
      );
      console.log("Raw expenses by location API response:", response);

      if (!response.data) {
        console.error("Expenses by location API response has no data property");
        return [];
      }

      // Check for 'expenses' property first
      if (response.data.expenses) {
        console.log(
          "Found expenses property in response, count:",
          response.data.expenses.length,
        );
        const expenses = response.data.expenses as EventDetails[];
        // Log a sample expense to see its structure
        if (expenses.length > 0) {
          console.log("Sample expense:", expenses[0]);
        }
        return expenses;
      }

      // If no expenses property exists, check if response.data is an array directly
      if (Array.isArray(response.data)) {
        console.log(
          "Response data is an array, returning directly, count:",
          response.data.length,
        );
        const expenses = response.data;
        // Log a sample expense to see its structure
        if (expenses.length > 0) {
          console.log("Sample expense:", expenses[0]);
        }
        return expenses;
      }

      console.error(
        "Could not find expenses data in the response:",
        response.data,
      );
      return [];
    } catch (error) {
      // Check if this is an abort error
      if (
        error instanceof Error &&
        (error.name === "AbortError" || (error as any).code === "ECONNABORTED")
      ) {
        console.log(
          `Expenses by location API request was cancelled for location: ${locationId}`,
        );
        return [];
      }
      console.error("Error fetching expenses by location:", error);
      throw error;
    }
  },

  // New function to get expenses by contact ID
  getExpensesByContact: async (
    contactId: string | number,
  ): Promise<EventDetails[]> => {
    try {
      console.log(
        `Making API call to fetch expenses for contact ID: ${contactId}...`,
      );
      const response = await apiClient.post("/expenses/api/by-contact/", {
        contact_id: contactId,
      });
      console.log("Raw expenses by contact API response:", response);

      if (!response.data) {
        console.error("Expenses by contact API response has no data property");
        return [];
      }

      // Check for 'expenses' property first
      if (response.data.expenses) {
        console.log("Found expenses property in response");
        return response.data.expenses as EventDetails[];
      }

      // If no expenses property exists, check if response.data is an array directly
      if (Array.isArray(response.data)) {
        console.log("Response data is an array, returning directly");
        return response.data;
      }

      console.error(
        "Could not find expenses data in the response:",
        response.data,
      );
      return [];
    } catch (error) {
      console.error("Error fetching expenses by contact:", error);
      throw new Error("Error fetching expenses by contact");
    }
  },

  createExpense: async (
    payload: CreateExpensePayload,
  ): Promise<EventDetails> => {
    try {
      const response = await apiClient.post("/expenses/api/create/", payload);
      console.log("Expense created:", response.data);

      return response.data as EventDetails;
    } catch (error) {
      console.error("Error creating expense:", error);
      throw new Error("Error creating expense");
    }
  },

  createParentExpense: async (
    payload: CreateParentExpensePayload,
  ): Promise<EventDetails> => {
    try {
      console.log("Creating parent expense with payload:", payload);

      const response = await apiClient.post(
        "/expenses/api/parent/create-with-children/",
        payload,
      );
      console.log("Parent expense created:", response.data);

      return response.data as EventDetails;
    } catch (error) {
      console.error("Error creating parent expense:", error);
      throw new Error("Error creating parent expense");
    }
  },

  updateExpense: async (
    id: string | number,
    payload: CreateExpensePayload,
  ): Promise<EventDetails> => {
    try {
      const response = await apiClient.post(
        `/expenses/api/updateevent/${id}/`,
        payload,
      );
      console.log("Expense updated:", response.data);

      return response.data as EventDetails;
    } catch (error) {
      console.error("Error updating expense:", error);
      throw new Error("Error updating expense");
    }
  },

  softDeleteExpense: async (expenseId: string): Promise<void> => {
    try {
      console.log("Sending soft delete request for expense:", expenseId);
      // Show loading indicator or buffer before the request
      const loadingToast = toast.loading("Deleting expense...");

      try {
        const response = await apiClient.delete(
          `/expenses/api/softdelete/${expenseId}/`,
        );

        // Remove loading indicator
        toast.dismiss(loadingToast);

        // Check response status
        if (response.status === 200) {
          toast.success("Expense has been deleted successfully");
        }
      } catch (error: any) {
        // Remove loading indicator
        toast.dismiss(loadingToast);

        // Handle 409 conflict error specifically
        if (error.response && error.response.status === 409) {
          console.log("409 error data:", error.response.data);

          if (error.response.data.warning) {
            toast.error(error.response.data.warning);
          } else if (error.response.data.message) {
            toast.error(error.response.data.message);
          } else {
            toast.error("Conflict occurred while deleting expense");
          }
        } else {
          // Handle other errors
          toast.error("Failed to delete expense. Please try again.");
          console.error("Error soft deleting expense:", error);
        }
        throw error; // Re-throw for the caller to handle if needed
      }
    } catch (error) {
      console.error("Error soft deleting expense:", error);
      throw error;
    }
  },

  // update expense status
  updateExpenseStatus: async (
    expenseId: string | number,
    payload: string | { status: string; paid_date?: string },
  ): Promise<EventDetails> => {
    try {
      const requestBody =
        typeof payload === "string"
          ? { event_id: expenseId, status: payload }
          : { event_id: expenseId, ...payload };

      const response = await apiClient.post(
        "/expenses/api/updatestatus/",
        requestBody,
      );
      console.log("Expense status updated:", response.data);
      return response.data as EventDetails;
    } catch (error) {
      console.error("Error updating expense status:", error);
      throw new Error("Error updating expense status");
    }
  },
});

export default ExpenseServices;
