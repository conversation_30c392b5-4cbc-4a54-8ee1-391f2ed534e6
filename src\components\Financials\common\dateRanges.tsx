import React from "react";
import useLanguage from "@/hooks/useLanguage";

export const formatDate = (date: Date) => {
    return date.toISOString().split('T')[0];
};

export const getDateRange = (range: string) => {
    const currentDate = new Date();
    const currentYear = currentDate.getFullYear();
    const currentMonth = currentDate.getMonth();
    let start: Date;
    let end: Date;

    switch (range) {
        case "thisMonth":
            start = new Date(currentYear, currentMonth, 1);
            end = new Date(currentYear, currentMonth + 1, 0);
            break;
        case "lastMonth":
            start = new Date(currentYear, currentMonth - 1, 1);
            end = new Date(currentYear, currentMonth, 0);
            break;
        case "nextMonth":
            start = new Date(currentYear, currentMonth + 1, 1);
            end = new Date(currentYear, currentMonth + 2, 0);
            break;
        case "1stQuarter":
            start = new Date(currentYear, 0, 1);
            end = new Date(currentYear, 3, 0);
            break;
        case "2ndQuarter":
            start = new Date(currentYear, 3, 1);
            end = new Date(currentYear, 6, 0);
            break;
        case "3rdQuarter":
            start = new Date(currentYear, 6, 1);
            end = new Date(currentYear, 9, 0);
            break;
        case "4thQuarter":
            start = new Date(currentYear, 9, 1);
            end = new Date(currentYear, 12, 0);
            break;
        case "1stHalf":
            start = new Date(currentYear, 0, 1);
            end = new Date(currentYear, 6, 0);
            break;
        case "2ndHalf":
            start = new Date(currentYear, 6, 1);
            end = new Date(currentYear, 12, 0);
            break;
        case "thisYear":
            start = new Date(currentYear, 0, 1);
            end = new Date(currentYear, 12, 31);
            break;
        case "lastYear":
            start = new Date(currentYear - 1, 0, 1);
            end = new Date(currentYear - 1, 12, 31);
            break;
        case "nextYear":
            start = new Date(currentYear + 1, 0, 1);
            end = new Date(currentYear + 1, 12, 31);
            break;
        case "last7Days":
            start = new Date(currentDate);
            start.setDate(currentDate.getDate() - 7);
            end = currentDate;
            break;
        case "next7Days":
            start = currentDate;
            end = new Date(currentDate);
            end.setDate(currentDate.getDate() + 7);
            break;
        case "last30Days":
            start = new Date(currentDate);
            start.setDate(currentDate.getDate() - 30);
            end = currentDate;
            break;
        case "next30Days":
            start = currentDate;
            end = new Date(currentDate);
            end.setDate(currentDate.getDate() + 30);
            break;
        case "last90Days":
            start = new Date(currentDate);
            start.setDate(currentDate.getDate() - 90);
            end = currentDate;
            break;
        case "next90Days":
            start = currentDate;
            end = new Date(currentDate);
            end.setDate(currentDate.getDate() + 90);
            break;
        case "last6Months":
            start = new Date(currentDate);
            start.setMonth(currentDate.getMonth() - 6);
            end = currentDate;
            break;
        case "next6Months":
            start = currentDate;
            end = new Date(currentDate);
            end.setMonth(currentDate.getMonth() + 6);
            break;
        case "15DaysBeforeAndAfter":
            start = new Date(currentDate);
            start.setDate(currentDate.getDate() - 15);
            end = new Date(currentDate);
            end.setDate(currentDate.getDate() + 15);
            break;
        default:
            start = new Date(currentDate);
            start.setDate(currentDate.getDate() - 15);
            end = new Date(currentDate);
            end.setDate(currentDate.getDate() + 15);
    }

    return { start: formatDate(start), end: formatDate(end) };
};

interface DateRangeSelectorProps {
    onChange: (range: string) => void;
}

export const DateRangeSelector: React.FC<DateRangeSelectorProps> = ({ onChange }) => {
    const { t } = useLanguage();

    return (
        <div className="mb-4">
            <label className="mr-2">{t("Quick Date Range")}:</label>
            <select onChange={(e) => onChange(e.target.value)} className="border rounded p-2 dark:bg-gray-800 dark:text-gray-200 dark:border-gray-600">
                <option value="">{t("Select Range")}</option>
                <option value="thisMonth">{t("This Month")}</option>
                <option value="lastMonth">{t("Last Month")}</option>
                <option value="nextMonth">{t("Next Month")}</option>
                <option value="1stQuarter">{t("1st Quarter")}</option>
                <option value="2ndQuarter">{t("2nd Quarter")}</option>
                <option value="3rdQuarter">{t("3rd Quarter")}</option>
                <option value="4thQuarter">{t("4th Quarter")}</option>
                <option value="1stHalf">{t("1st Half")}</option>
                <option value="2ndHalf">{t("2nd Half")}</option>
                <option value="thisYear">{t("This Year")}</option>
                <option value="lastYear">{t("Last Year")}</option>
                <option value="nextYear">{t("Next Year")}</option>
                <option value="last7Days">{t("Last 7 Days")}</option>
                <option value="next7Days">{t("Next 7 Days")}</option>
                <option value="last30Days">{t("Last 30 Days")}</option>
                <option value="next30Days">{t("Next 30 Days")}</option>
                <option value="last90Days">{t("Last 90 Days")}</option>
                <option value="next90Days">{t("Next 90 Days")}</option>
                <option value="last6Months">{t("Last 6 Months")}</option>
                <option value="next6Months">{t("Next 6 Months")}</option>
                <option value="15DaysBeforeAndAfter">{t("15 Days Before and After")}</option>
            </select>
        </div>
    );
};