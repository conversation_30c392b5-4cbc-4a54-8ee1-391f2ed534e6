import { <PERSON>ada<PERSON> } from "next";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/authOptions";
import { redirect } from "next/navigation";

import Layout from "@/components/Layouts/Layout";
import Dashboard from "@/components/Dashboard";

export default async function DashboardPage() {
  const session = await getServerSession(authOptions);
  return (
    <Layout>
      <Dashboard />
    </Layout>
  );
}