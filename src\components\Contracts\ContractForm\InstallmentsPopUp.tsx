import { DataImporter } from "@/components/Importer/DataImporter";
import React, { useState, useEffect } from "react";

interface InstallmentsPopUpProps {
  onSubmit: (
    data: {
      amount: number;
      due_date: string;
      notification_view_date: string;
      status?: string;
      priority?: string;
    }[],
  ) => void;
  onClose: () => void;
  data: any;
}
interface Installment {
  amount: number;
  due_date: string;
  notification_view_date: string;
  status?: string;
  priority?: string;
}

const InstallmentsPopUp: React.FC<InstallmentsPopUpProps> = ({
  onSubmit,
  onClose,
  data,
}) => {
  const [installments, setInstallments] = useState<
    {
      amount: number;
      due_date: string;
      notification_view_date: string;
      status?: string;
      priority?: string;
    }[]
  >([]);
  const [totalAmount, setTotalAmount] = useState<number>(0);
  const [startDate, setStartDate] = useState<string>("");
  const [installmentCount, setInstallmentCount] = useState<number>(1);
  const [isImorting, setIsImporting] = useState<boolean>(false);
  useEffect(() => {
    if (data) {
      if (data.contract) {
        setTotalAmount(parseFloat(data.contract.total_amount) || 0);
        setStartDate(data.contract.start_date || "");
      }
      if (data.installments && data.installments.length > 0) {
        // Use existing installments from contract financial data
        setInstallments(data.installments);
        setInstallmentCount(data.installments.length);
      } else if (
        data.contractFinancials &&
        data.contractFinancials.length > 0
      ) {
        // Transform contract financials (expense data) to installments format
        const transformedInstallments = data.contractFinancials.map(
          (financial: any) => ({
            amount: financial.amount || 0,
            due_date: financial.dueDate || financial.due_date || "",
            notification_view_date:
              financial.dueDate || financial.due_date || "",
            status: financial.status || "pending",
            priority: financial.priority || "medium",
          }),
        );
        setInstallments(transformedInstallments);
        setInstallmentCount(transformedInstallments.length);
      }
    }
  }, [data]);

  // Debug: Log installments whenever they change
  useEffect(() => {
    console.log("Installments state changed:", installments);
  }, [installments]);
  const handleAddInstallment = () => {
    const lastInstallment = installments[installments.length - 1];
    const newDueDate = lastInstallment
      ? (() => {
          const date = new Date(lastInstallment.due_date);
          date.setMonth(date.getMonth() + 1);
          return date.toISOString().split("T")[0];
        })()
      : startDate;

    setInstallments([
      ...installments,
      {
        amount: 0,
        due_date: newDueDate,
        notification_view_date: newDueDate,
        status: "pending",
      },
    ]);
  };

  const handleRemoveInstallment = (index: number) => {
    const updatedInstallments = installments.filter((_, i) => i !== index);
    setInstallments(updatedInstallments);
  };

  const generateEqualInstallments = () => {
    if (totalAmount <= 0 || installmentCount <= 0 || !startDate) {
      alert("Total amount, installment count, and start date must be valid.");
      return;
    }

    const exactAmount = totalAmount / installmentCount;
    const formattedAmount = parseFloat(exactAmount.toFixed(2));
    const totalAfterFormatting = formattedAmount * installmentCount;
    const roundingDifference = parseFloat(
      (totalAmount - totalAfterFormatting).toFixed(2),
    );

    const startDateObj = new Date(startDate);
    const newInstallments = Array(installmentCount)
      .fill(null)
      .map((_, index) => {
        const dueDate = new Date(startDateObj);
        dueDate.setMonth(dueDate.getMonth() + index);
        const installmentAmount =
          index === installmentCount - 1
            ? formattedAmount + roundingDifference
            : formattedAmount;
        return {
          amount: parseFloat(installmentAmount.toFixed(2)),
          due_date: dueDate.toISOString().split("T")[0],
          notification_view_date: dueDate.toISOString().split("T")[0],
          status: "pending",
        };
      });

    setInstallments(newInstallments);
  };

  const handleAmountChange = (index: number, value: number) => {
    const newInstallments = [...installments];
    newInstallments[index].amount = value;
    setInstallments(newInstallments);
  };
  const handleDueDateChange = (index: number, value: string) => {
    const newInstallments = [...installments];
    newInstallments[index].due_date = value;
    // Automatically set notification_view_date to match due_date
    newInstallments[index].notification_view_date = value;
    setInstallments(newInstallments);
  };

  const handleNotificationViewDateChange = (index: number, value: string) => {
    const newInstallments = [...installments];
    newInstallments[index].notification_view_date = value;
    setInstallments(newInstallments);
  };
  const handleStatusChange = (index: number, value: string) => {
    const newInstallments = [...installments];
    newInstallments[index].status = value;
    setInstallments(newInstallments);
  };

  const handlePriorityChange = (index: number, value: string) => {
    const newInstallments = [...installments];
    newInstallments[index].priority = value;
    setInstallments(newInstallments);
  };

  const handleImport = (importedData: any[]) => {
    const formattedData = importedData.map((item) => ({
      amount: item.amount || 0,
      due_date: item.due_date || "",
      notification_view_date: item.notification_view_date || "",
      status: item.status || "pending",
      priority: item.priority || "medium",
    }));
    setInstallments(formattedData);
    setIsImporting(false);
    onSubmit(formattedData);
    onClose();
  };

  const handleSubmit = () => {
    onSubmit(installments);
    onClose();
  };

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
      <div className="max-h-[80vh] w-full max-w-4xl overflow-y-auto rounded-lg bg-white p-6 shadow-lg">
        <h2 className="mb-4 text-lg font-semibold">Manage Installments</h2>
        <div className="mb-4">
          <button
            onClick={() => setIsImporting(!isImorting)}
            className="mb-4 rounded bg-blue-600 px-4 py-2 text-white hover:bg-blue-700"
          >
            {isImorting ? "Cancel Import" : "Import Installments"}
          </button>
        </div>
        {isImorting ? (
          <DataImporter<Installment[]>
            dateFields={["due_date", "notification_view_date"]}
            templateFields={["amount"]}
            manualDropdowns={[
              {
                field: "status",
                options: [
                  "upcoming",
                  "pending",
                  "completed",
                  "overdue",
                  "cancelled",
                ],
              },
              {
                field: "priority",
                options: ["low", "medium", "high"],
              },
            ]}
            onImport={handleImport}
          />
        ) : (
          <>
            <div className="mb-4">
              <label className="mb-2 block text-sm font-medium">
                Total Amount
              </label>
              <input
                type="number"
                value={totalAmount}
                onChange={(e) => setTotalAmount(parseFloat(e.target.value))}
                className="w-full border p-2"
              />
            </div>
            <div className="mb-4">
              <label className="mb-2 block text-sm font-medium">
                Start Date
              </label>
              <input
                type="date"
                value={startDate}
                onChange={(e) => setStartDate(e.target.value)}
                className="w-full border p-2"
              />
            </div>
            <div className="mb-4">
              <label className="mb-2 block text-sm font-medium">
                Number of Installments
              </label>
              <input
                type="number"
                value={installmentCount}
                onChange={(e) =>
                  setInstallmentCount(Math.max(1, parseInt(e.target.value, 10)))
                }
                className="w-full border p-2"
                min="1"
              />
            </div>
            <button
              onClick={generateEqualInstallments}
              className="mb-4 rounded bg-green-600 px-4 py-2 text-white hover:bg-green-700"
            >
              Generate Equal Installments
            </button>{" "}
            {installments.map((installment, index) => (
              <div
                key={`installment-${index}`}
                className="mb-4 rounded border p-4"
              >
                <div className="mb-2">
                  <label className="mb-1 block text-sm font-medium">
                    Amount
                  </label>
                  <input
                    type="number"
                    placeholder="Amount"
                    value={installment.amount}
                    onChange={(e) =>
                      handleAmountChange(index, parseFloat(e.target.value))
                    }
                    className="w-full rounded border p-2"
                  />
                </div>
                <div className="mb-2">
                  <label className="mb-1 block text-sm font-medium">
                    Due Date
                  </label>
                  <input
                    type="date"
                    placeholder="Due Date"
                    value={installment.due_date}
                    onChange={(e) => handleDueDateChange(index, e.target.value)}
                    className="w-full rounded border p-2"
                  />
                </div>
                <div className="mb-2">
                  <label className="mb-1 block text-sm font-medium">
                    Notification Date
                  </label>
                  <input
                    type="date"
                    placeholder="Notification View Date"
                    value={installment.notification_view_date}
                    onChange={(e) =>
                      handleNotificationViewDateChange(index, e.target.value)
                    }
                    className="w-full rounded border bg-gray-100 p-2"
                    disabled
                    title="Notification date automatically matches due date"
                  />
                </div>{" "}
                <div className="mb-2">
                  <label className="mb-1 block text-sm font-medium">
                    Status
                  </label>
                  <select
                    value={installment.status || ""}
                    onChange={(e) => handleStatusChange(index, e.target.value)}
                    className="w-full rounded border p-2"
                  >
                    <option value="">Select Status</option>
                    <option value="upcoming">Upcoming</option>
                    <option value="pending">Pending</option>
                    <option value="completed">Completed</option>
                    <option value="overdue">Overdue</option>
                    <option value="cancelled">Cancelled</option>
                  </select>
                </div>
                <button
                  onClick={() => handleRemoveInstallment(index)}
                  className="rounded bg-red-600 px-4 py-2 text-white hover:bg-red-700"
                >
                  Remove
                </button>
              </div>
            ))}
            <button
              onClick={handleAddInstallment}
              className="mb-4 rounded bg-blue-600 px-4 py-2 text-white hover:bg-blue-700"
            >
              Add Installment
            </button>
            <div className="mt-6 flex justify-end space-x-4">
              <button
                onClick={onClose}
                className="rounded bg-gray-200 px-4 py-2 text-gray-700 hover:bg-gray-300"
              >
                Cancel
              </button>
              <button
                onClick={handleSubmit}
                className="rounded bg-blue-600 px-4 py-2 text-white hover:bg-blue-700"
              >
                Submit
              </button>
            </div>
          </>
        )}
      </div>
    </div>
  );
};

export default InstallmentsPopUp;
