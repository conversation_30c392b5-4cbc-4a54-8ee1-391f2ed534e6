import { X, Plus, Check } from "lucide-react";
import { Card, CardContent } from "@/components/cards/card";
import { Contact } from "@/lib/types/contacts";
import React, { useState, useEffect } from "react";
import { createPortal } from "react-dom";
import useLanguage from "@/hooks/useLanguage";
import EditConfirmationModal from "../Modals/EditConfirmationModal";
import { useContactServices } from "@/hooks/useContact";
import { ContactType, CreateContactTypePayload } from "@/lib/contacts";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { DataImporter } from "../Importer/DataImporter";

interface ContactFormProps {
  isOpen: boolean;
  onClose: () => void;
  contact?: Contact;
  onSubmit: (data: Partial<Contact>) => void;
  view?: "modal" | "sidebar";
}

const ContactForm: React.FC<ContactFormProps> = ({
  isOpen,
  onClose,
  contact,
  onSubmit,
  view = "modal",
}) => {
  const [mounted, setMounted] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [selectedTypeIds, setSelectedTypeIds] = useState<string[]>(
    contact?.type || [],
  );
  const [filteredOptions, setFilteredOptions] = useState<ContactType[]>([]);
  const [isDropdownOpen, setIsDropdownOpen] = useState<boolean>(false);
  const [isAddingCustomType, setIsAddingCustomType] = useState<boolean>(false);
  const [customType, setCustomType] = useState<string>("");
  const [searchValue, setSearchValue] = useState<string>("");
  const { t } = useLanguage();
  const contactServices = useContactServices();
  const [isImporting, setIsImporting] = useState(false);
  const queryClient = useQueryClient();

  const [formData, setFormData] = useState<Partial<Contact>>({
    name: contact?.name || "",
    company: contact?.company || "",
    email: contact?.email || "",
    phone: contact?.phone || "",
    address: contact?.address || "",
    type: contact?.type || [],
  });

  // Fetch contact types from API - it now returns ContactType[]
  const { data: contactTypes = [], isLoading: typesLoading } = useQuery({
    queryKey: ["contactTypes"],
    queryFn: async () => {
      try {
        const types = await contactServices.getAllContactTypes();
        console.log("API returned contact types:", types);
        return types;
      } catch (error) {
        console.error("Error fetching contact types:", error);
        return [];
      }
    },
  });
  const handleImportedContacts = (imported: Partial<Contact>[]) => {
  // Example: loop and submit or add to state
  imported.forEach(onSubmit); // Or add to a table for review
};

  const createTypeMutation = useMutation({
    mutationFn: async (payload: CreateContactTypePayload) => {
      return await contactServices.createContactType(payload);
    },
    onSuccess: (data) => {
      // When successful, invalidate the contactTypes query to trigger a refresh
      queryClient.invalidateQueries({ queryKey: ["contactTypes"] });

      // Add the new type ID to selected types
      if (data && data.id) {
        setSelectedTypeIds((prev) => [...prev, data.id]);
      }
    },
    onError: (error) => {
      console.error("Error creating contact type:", error);
      // You could add error handling UI here
    },
  });

  // Update filtered options whenever contact types change or search value changes
  useEffect(() => {
    if (contactTypes.length > 0) {
      setFilteredOptions(
        contactTypes.filter((type) =>
          type.name.toLowerCase().includes(searchValue.toLowerCase()),
        ),
      );
    }
  }, [contactTypes, searchValue]);

  useEffect(() => {
    setMounted(true);
    if (isOpen && view === "modal") {
      document.body.style.overflow = "hidden";
      window.scrollTo({ top: 0, behavior: "smooth" });
    }
    return () => {
      document.body.style.overflow = "unset";
    };
  }, [isOpen, view]);

  const handleAddCustomType = async () => {
    if (customType.trim()) {
      try {
        // Create payload
        const payload: CreateContactTypePayload = {
          name: customType.trim().toLowerCase(),
        };

        // Use the mutation instead of directly calling the API
        await createTypeMutation.mutateAsync(payload);

        // The ID will be added in the onSuccess callback of the mutation

        // Reset the custom type input
        setCustomType("");
        setIsAddingCustomType(false);
      } catch (error) {
        console.error("Error creating contact type:", error);
        // You could add error handling UI here
      }
    }
  };

  useEffect(() => {
    if (contact) {
      setFormData({
        name: contact.name,
        company: contact.company,
        email: contact.email,
        phone: contact.phone,
        address: contact.address,
        type: contact.type,
      });
      setSelectedTypeIds(contact.type);
    }
  }, [contact]);

  const handleInputChange = (value: string) => {
    setSearchValue(value);
  };

  const handleOptionToggle = (typeId: string) => {
    setSelectedTypeIds((prev) =>
      prev.includes(typeId)
        ? prev.filter((item) => item !== typeId)
        : [...prev, typeId],
    );
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    const form = e.target as HTMLFormElement;
    const newFormData: Partial<Contact> = {
      name: (form.elements.namedItem("name") as HTMLInputElement).value,
      company: (form.elements.namedItem("company") as HTMLInputElement).value,
      email: (form.elements.namedItem("email") as HTMLInputElement).value,
      phone: (form.elements.namedItem("phone") as HTMLInputElement).value,
      address: (form.elements.namedItem("address") as HTMLInputElement).value,
      type: selectedTypeIds, // Use type IDs
    };
    setFormData(newFormData);

    if (contact) {
      setIsEditModalOpen(true);
    } else {
      onSubmit(newFormData);
      onClose();
    }
  };

  // Get type names for display
  const getTypeNameById = (id: string): string => {
    const foundType = contactTypes.find((type) => type.id === id);
    return foundType ? foundType.name : id;
  };

  const getSelectedTypeNames = (): string => {
    return selectedTypeIds
      .map((id) => {
        const name = getTypeNameById(id);
        return name.charAt(0).toUpperCase() + name.slice(1);
      })
      .join(", ");
  };

  if (!mounted || !isOpen) return null;

  const renderForm = () => (
    <form onSubmit={handleSubmit} className="space-y-6">
    {!contact && ( 
      <div className="mb-6 flex items-center justify-between">
        <div>
          <h3 className="text-lg font-semibold">{t("importContacts")}</h3>
          <p className="text-sm text-gray-500 dark:text-gray-400">
            {t("importContactsDescription")}
          </p>
        </div>
        <button
          type="button"
          onClick={() => setIsImporting((prev) => !prev)}
          className={`rounded-lg px-4 py-2 text-white ${
            isImporting
              ? "bg-red-600 hover:bg-red-700"
              : "bg-blue-600 hover:bg-blue-700"
          }`}
        >
          {isImporting ? t("cancelImport") : t("startImport")}
        </button>
      </div>
    )}


      { isImporting ? (
        <div className="my-6">
          <h3 className="text-lg font-semibold">{t("Import Contacts")}</h3>
            <DataImporter<Partial<Contact>>
              templateFields={["name", "email", "phone", "company", "address"]}
              onImport={handleImportedContacts}
              manualDropdowns={[{ field: "contactType", options: ["agent", "company", "buyer"] }]}
            />

        </div>
      ):(
      <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
        <div>
          <label
            htmlFor="name"
            className="mb-1 block text-sm font-medium text-gray-700 dark:text-gray-300"
          >
            {t("name")}
          </label>
          <input
            id="name"
            name="name"
            type="text"
            defaultValue={contact?.name}
            className="w-full rounded-lg border p-2 focus:outline-none focus:ring-2 focus:ring-blue-500 dark:border-gray-700 dark:bg-gray-800 dark:text-gray-100"
          />
        </div>

        <div>
          <label
            htmlFor="company"
            className="mb-1 block text-sm font-medium text-gray-700 dark:text-gray-300"
          >
            {t("company")}
          </label>
          <input
            id="company"
            name="company"
            type="text"
            defaultValue={contact?.company}
            className="w-full rounded-lg border p-2 focus:outline-none focus:ring-2 focus:ring-blue-500 dark:border-gray-700 dark:bg-gray-800 dark:text-gray-100"
          />
        </div>

        <div>
          <label
            htmlFor="email"
            className="mb-1 block text-sm font-medium text-gray-700 dark:text-gray-300"
          >
            {t("email")}
          </label>
          <input
            id="email"
            name="email"
            type="email"
            defaultValue={contact?.email}
            className="w-full rounded-lg border p-2 focus:outline-none focus:ring-2 focus:ring-blue-500 dark:border-gray-700 dark:bg-gray-800 dark:text-gray-100"
          />
        </div>

        <div>
          <label
            htmlFor="phone"
            className="mb-1 block text-sm font-medium text-gray-700 dark:text-gray-300"
          >
            {t("phone")}
          </label>
          <input
            id="phone"
            name="phone"
            type="tel"
            defaultValue={contact?.phone}
            className="w-full rounded-lg border p-2 focus:outline-none focus:ring-2 focus:ring-blue-500 dark:border-gray-700 dark:bg-gray-800 dark:text-gray-100"
          />
        </div>

        <div className="md:col-span-2">
          <label
            htmlFor="address"
            className="mb-1 block text-sm font-medium text-gray-700 dark:text-gray-300"
          >
            {t("address")}
          </label>
          <input
            id="address"
            name="address"
            type="text"
            defaultValue={contact?.address}
            className="w-full rounded-lg border p-2 focus:outline-none focus:ring-2 focus:ring-blue-500 dark:border-gray-700 dark:bg-gray-800 dark:text-gray-100"
          />
        </div>
        <div>
          <label className="mb-1 block text-sm font-medium text-gray-700 dark:text-gray-300">
            {t("type")}
          </label>
          <div className="relative">
            {isAddingCustomType ? (
              <div className="flex items-center gap-2">
                <input
                  type="text"
                  value={customType}
                  onChange={(e) => setCustomType(e.target.value)}
                  className="w-full rounded-lg border p-2 focus:outline-none focus:ring-2 focus:ring-blue-500 dark:border-gray-700 dark:bg-gray-800 dark:text-gray-100"
                  placeholder={t("enterCustomType")}
                  autoFocus
                />
                <button
                  type="button"
                  onClick={handleAddCustomType}
                  className="rounded-lg bg-blue-600 p-2 text-white hover:bg-blue-700"
                >
                  <Check size={16} />
                </button>
                <button
                  type="button"
                  onClick={() => setIsAddingCustomType(false)}
                  className="rounded-lg bg-gray-200 p-2 text-gray-700 hover:bg-gray-300 dark:bg-gray-700 dark:text-gray-300 dark:hover:bg-gray-600"
                >
                  <X size={16} />
                </button>
              </div>
            ) : (
              <div className="flex items-center gap-2">
                <div
                  className="w-full cursor-pointer rounded-lg border p-2 focus:outline-none focus:ring-2 focus:ring-blue-500 dark:border-gray-700 dark:bg-gray-800 dark:text-gray-100"
                  onClick={() => setIsDropdownOpen((prev) => !prev)}
                >
                  {selectedTypeIds.length > 0
                    ? getSelectedTypeNames()
                    : t("selectOneOrMoreTypes")}
                </div>
                <button
                  type="button"
                  onClick={() => setIsAddingCustomType(true)}
                  className="rounded-lg bg-blue-600 p-2 text-white hover:bg-blue-700"
                  title={t("addCustomType")}
                >
                  <Plus size={16} />
                </button>
              </div>
            )}

            {isDropdownOpen && !isAddingCustomType && (
              <div className="absolute z-10 mt-2 max-h-40 w-full overflow-hidden rounded-lg border bg-white shadow-md dark:bg-gray-800">
                <input
                  type="text"
                  className="w-full border-b p-2 focus:outline-none focus:ring-2 focus:ring-blue-500 dark:border-gray-700 dark:bg-gray-800 dark:text-gray-100"
                  placeholder={t("searchTypes")}
                  value={searchValue}
                  onChange={(e) => handleInputChange(e.target.value)}
                />

                <ul className="max-h-32 overflow-y-auto">
                  {typesLoading ? (
                    <li className="p-2 text-center text-gray-500">
                      {t("loading")}
                    </li>
                  ) : filteredOptions.length > 0 ? (
                    filteredOptions.map((type) => (
                      <li
                        key={type.id}
                        onClick={() => handleOptionToggle(type.id)}
                        className={`flex cursor-pointer items-center p-2 hover:bg-blue-100 dark:hover:bg-gray-700 ${
                          selectedTypeIds.includes(type.id)
                            ? "bg-blue-50 dark:bg-gray-700"
                            : ""
                        }`}
                      >
                        <input
                          type="checkbox"
                          checked={selectedTypeIds.includes(type.id)}
                          onChange={() => handleOptionToggle(type.id)}
                          className="pointer-events-none mr-2"
                        />
                        {type.name.charAt(0).toUpperCase() + type.name.slice(1)}
                      </li>
                    ))
                  ) : (
                    <li className="p-2 text-center text-gray-500">
                      {t("noTypesFound")}
                    </li>
                  )}
                </ul>
              </div>
            )}
          </div>
        </div>
      </div>
      )}
      {!isImporting && (
              <div className="flex justify-end gap-4">
        <button
          type="button"
          onClick={onClose}
          className="rounded-lg border px-4 py-2 hover:bg-gray-50 dark:text-gray-100 dark:hover:bg-gray-700"
        >
          {t("cancel")}
        </button>
        <button
          type="submit"
          className="rounded-lg bg-blue-600 px-4 py-2 text-white hover:bg-blue-700 dark:bg-blue-700 dark:hover:bg-blue-800"
        >
          {contact ? t("saveChanges") : t("addContact")}
        </button>
      </div>

      )}

    </form>
  );

  // Modal view
  if (view === "modal") {
    return createPortal(
      <div className="fixed inset-0 z-[9999] flex items-center justify-center">
        <div
          className="fixed inset-0 bg-black/50 backdrop-blur-sm"
          onClick={onClose}
        />
        <div className="relative">
          <Card className="mx-4 w-full max-w-2xl overflow-hidden bg-white dark:bg-boxdark">
            <CardContent className="max-h-[90vh] overflow-y-auto p-6">
              <div className="mb-6 flex items-center justify-between">
                <h2 className="text-xl font-bold dark:text-gray-100">
                  {contact ? t("editContact") : t("addNewContact")}
                </h2>
                <button
                  onClick={onClose}
                  className="rounded-lg p-2 transition-colors hover:bg-gray-100 dark:hover:bg-gray-700"
                >
                  <X size={20} className="text-gray-600 dark:text-gray-400" />
                </button>
              </div>

              {renderForm()}

              {contact && (
                <EditConfirmationModal
                  isOpen={isEditModalOpen}
                  onClose={() => setIsEditModalOpen(false)}
                  onConfirm={() => {
                    onSubmit(formData);
                    setIsEditModalOpen(false);
                    onClose();
                  }}
                  title={t("editContact")}
                  itemName={contact.name}
                />
              )}
            </CardContent>
          </Card>
        </div>
      </div>,
      document.body,
    );
  }

  // Sidebar view (inline editing)
  return (
    <div className="rounded-lg bg-white p-6 shadow-md dark:bg-boxdark">
      <div className="mb-6 flex items-center justify-between">
        <h2 className="text-xl font-bold dark:text-gray-100">
          {contact ? t("editContact") : t("addNewContact")}
        </h2>
        <button
          onClick={onClose}
          className="rounded-lg p-2 transition-colors hover:bg-gray-100 dark:hover:bg-gray-700"
        >
          <X size={20} className="text-gray-600 dark:text-gray-400" />
        </button>
      </div>

      {renderForm()}

      {contact && (
        <EditConfirmationModal
          isOpen={isEditModalOpen}
          onClose={() => setIsEditModalOpen(false)}
          onConfirm={() => {
            onSubmit(formData);
            setIsEditModalOpen(false);
            onClose();
          }}
          title={t("editContact")}
          itemName={contact.name}
        />
      )}
    </div>
  );
};

export default ContactForm;
