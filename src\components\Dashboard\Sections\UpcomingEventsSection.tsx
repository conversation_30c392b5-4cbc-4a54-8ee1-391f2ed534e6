import React, { useState } from "react";
import { FaCalendarAlt, FaMoneyBillAlt } from "react-icons/fa";
import { EventDetails } from "@/lib/interfaces/finaces";
import useLanguage from "@/hooks/useLanguage";
import UpcomingEventPopup from "@/components/Financials/common/UpcommingEventPopup.tsx";

interface UpcomingEventsSectionProps {
    events: EventDetails[];
    filterStatus: string;
    filterPriority: string;
    onFilterStatusChange: (status: string) => void;
    onFilterPriorityChange: (priority: string) => void;
    onSave: (updatedEvent: EventDetails) => void; // Add this prop for saving changes
}

const UpcomingEventsSection: React.FC<UpcomingEventsSectionProps> = ({
    events,
    filterStatus,
    filterPriority,
    onFilterStatusChange,
    onFilterPriorityChange,
    onSave,
}) => {
    const { t } = useLanguage();
    const [selectedEvent, setSelectedEvent] = useState<EventDetails | null>(null);

    const filteredEvents = events.filter((event) => {
        const statusMatch = filterStatus === "all" || event.status === filterStatus;
        const priorityMatch = filterPriority === "all" || event.priority === filterPriority;
        return statusMatch && priorityMatch;
    });

    // Show only the first 4 events
    const displayedEvents = filteredEvents.slice(0, 4);

    const handleEventClick = (event: EventDetails) => {
        const page = event.category === "income" ? "/finances/income" : "/finances/expenses";
        window.location.href = `${page}?eventId=${event.id}`;
    };

    return (
        <div className="bg-white p-6 border rounded-lg shadow-sm dark:bg-gray-800 dark:border-gray-700">
            <h2 className="text-xl font-semibold mb-6 dark:text-white text-center">
                {t("upcoming events")}
            </h2>

            {/* Event List */}
            <div className="space-y-4">
                {displayedEvents.map((event) => {
                    const amountStyle = event.category === "income"
                        ? "text-green-600 dark:text-green-400"
                        : "text-red-600 dark:text-red-400";

                    return (
                        <div
                            key={event.id}
                            className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-sm p-4 hover:shadow-md transition-shadow cursor-pointer"
                            onClick={() => handleEventClick(event)}
                        >
                            <div className="flex flex-col gap-2">
                                {/* Event Name */}
                                <h2 className="text-sm font-semibold dark:text-white">
                                    {event.title}
                                </h2>

                                {/* Event Date and Amount */}
                                <div className="flex items-center justify-between text-sm text-gray-600 dark:text-gray-400">
                                    <div className="flex items-center gap-2">
                                        <FaCalendarAlt className="w-4 h-4" />
                                        <span>{new Date(event.dueDate).toLocaleDateString()}</span>
                                    </div>
                                    <div className="flex items-center gap-2">
                                        <FaMoneyBillAlt className={amountStyle} />
                                        <span className={amountStyle}>${event.amount}</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    );
                })}
            </div>
            {/* View All Button */}
            {filteredEvents.length > 4 && (
                <a href="/finances" className="block w-full">
                    <button
                        className="mt-4 px-4 py-2 text-white bg-blue-500 rounded-lg shadow hover:bg-blue-600 text-center w-full"
                    >
                        {t("view all")}
                    </button>
                </a>
            )}

            {/* Event Popup */}
            {selectedEvent && (
                <UpcomingEventPopup
                    event={selectedEvent}
                    onClose={() => setSelectedEvent(null)}
                    onSave={onSave} // Pass the onSave function to handle updates
                />
            )}
        </div>
    );
};

export default UpcomingEventsSection;