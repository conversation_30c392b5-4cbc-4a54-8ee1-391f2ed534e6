import React from 'react';
import CalendarDay from './CalendarDay';
import { EventDetails } from "@/lib/interfaces/finaces";
import useLanguage from "@/hooks/useLanguage"; // Add this import

interface CalendarBodyProps {
  days: number[];
  year: number;
  month: number;
  viewType: string;
  events: EventDetails[];
  onDayClick: (day: number) => void;
}

const CalendarBody: React.FC<CalendarBodyProps> = ({ days, year, month, viewType, events, onDayClick }) => {
  const { t } = useLanguage(); // Use your translation hook

  const weeks = [];
  let week: JSX.Element[] = [];

  days.forEach((day, index) => {
    const date = new Date(year, month, day);
    // Filter events for this specific day
    const dayEvents = events.filter(event => {
      if (!event.dueDate) return false;
      const eventDate = new Date(event.dueDate);
      return eventDate.getFullYear() === date.getFullYear() &&
             eventDate.getMonth() === date.getMonth() &&
             eventDate.getDate() === date.getDate();
    });

    week.push(
      <CalendarDay
        key={index}
        day={day}
        year={year}
        viewType={viewType}
        events={dayEvents}
        onDayClick={onDayClick}
      />
    );

    if (week.length === 7) {
      weeks.push(<tr key={weeks.length}>{week}</tr>);
      week = [];
    }
  });

  // Fill the last week with empty cells if necessary
  if (week.length > 0) {
    while (week.length < 7) {
      week.push(<td key={`empty-${week.length}`} className="h-20 border border-stroke dark:border-strokedark"></td>);
    }
    weeks.push(<tr key={weeks.length}>{week}</tr>);
  }

  // Use translation for day names
  const dayNames = [
    t("Sun"),
    t("Mon"),
    t("Tue"),
    t("Wed"),
    t("Thu"),
    t("Fri"),
    t("Sat"),
  ];

  return (
    <table className="w-full">
      <thead>
        <tr className="text-white bg-gray-500 dark:bg-gray-900">
          {dayNames.map((day, index) => (
            <th
              key={day}
              className={`h-15 p-1 text-xs font-semibold sm:text-base xl:p-5 ${index < 6 ? 'border-r border-gray-300 dark:border-gray-700' : ''}`}
            >
              <span>{day}</span>
            </th>
          ))}
        </tr>
      </thead>
      <tbody>
        {weeks}
      </tbody>
    </table>
  );
};

export default CalendarBody;