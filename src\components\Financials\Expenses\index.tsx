"use client";
import React, { useState, useEffect, useCallback } from "react";
import { useSearchParams } from "next/navigation";
import Breadcrumb from "@/components/Breadcrumbs/Breadcrumb";
import useLanguage from "@/hooks/useLanguage";
import { EventDetails } from "@/lib/interfaces/finaces";
import ExpenseSummary from "./ExpenseSummary";
import ExpensePopup from "./ExpensePopup";
import UpcomingEventsPage from "@/components/Dashboard/UpcommingEventsPage/UpcommingEvents";
import DatePicker from "react-datepicker";
import "react-datepicker/dist/react-datepicker.css";
import { enUS, arSA } from "date-fns/locale";
import { getDateRange, DateRangeSelector } from "@/components/Financials/common/dateRanges";
import { LoadingComp } from "@/components/common/Loading";
import { usePermissions } from "@/hooks/usePermissions";
import ExpenseServices from "@/lib/expenses";

const ExpenseOverview: React.FC = () => {
  const { t, language } = useLanguage();
  const searchParams = useSearchParams();
  const [expenses, setExpenses] = useState<EventDetails[]>([]);
  const [selectedExpense, setSelectedExpense] = useState<EventDetails | null>(null);
  const [loading, setLoading] = useState(true);
  const { hasPermission, permissionsLoaded } = usePermissions();
  const { getExpenses } = ExpenseServices();

  // Default Date Range
  const currentDate = new Date();
  const [startDate, setStartDate] = useState<Date>(new Date(currentDate.setDate(currentDate.getDate() - 15)));
  const [endDate, setEndDate] = useState<Date>(new Date(new Date().setDate(new Date().getDate() + 15)));

  useEffect(() => {
    const fetchExpenses = async () => {
      setLoading(true);
      try {
        const result = await getExpenses();
        console.log("Fetched expenses:", result);
        setExpenses(result || []);
      } catch (err) {
        console.error("Failed to fetch expenses:", err);
      } finally {
        setLoading(false);
      }
    };
  
    fetchExpenses();
  }, []); 
  

  const filterExpensesByDate = (expenses: EventDetails[]) => {
    return expenses.filter((expense) => {
      const expenseDate = new Date(expense.dueDate);
      return expenseDate >= startDate && expenseDate <= endDate;
    });
  };

  // Change the parameter type from number to string
  const handleEditClick = (id: string) => {
    const expense = expenses.find((e) => e.id === id);
    if (expense) setSelectedExpense(expense);
  };

  const handlePopupClose = () => setSelectedExpense(null);

  const handleSavePopUp = (updatedExpense: EventDetails) => {
    setExpenses((prev) =>
      prev.map((expense) => (expense.id === updatedExpense.id ? updatedExpense : expense))
    );
    handlePopupClose();
  };

  const handleDateRangeChange = (range: string) => {
    const { start, end } = getDateRange(range);
    setStartDate(new Date(start));
    setEndDate(new Date(end));
  };

  const locale = language === "ar" ? arSA : enUS;
  const eventId = searchParams ? searchParams.get("eventId") : null;

  if (!permissionsLoaded) {
    return (
      <div className="fixed top-0 left-0 z-9999 flex h-screen w-full items-center justify-center bg-black dark:bg-boxdark">
        <LoadingComp />
      </div>
    );
  }

  return (
    <>
      <h2 className="text-2xl font-bold text-gray-900 dark:text-gray-400 mb-4 flex items-center">
        {t("Expense Overview")}
        <span className="text-sm font-normal text-gray-600 ml-2 flex items-center">
          <span className="mr-1">{t("as of")}</span>
          <DatePicker
            selected={startDate}
            onChange={(date) => date && setStartDate(date)}
            className="border rounded p-2 ml-1 dark:bg-gray-800 dark:text-gray-200 dark:border-gray-600"
            dateFormat="yyyy-MM-dd"
            locale={locale}
          />
          <span className="mx-1">{t("to")}</span>
          <DatePicker
            selected={endDate}
            onChange={(date) => date && setEndDate(date)}
            className="border rounded p-2 ml-1 dark:bg-gray-800 dark:text-gray-200 dark:border-gray-600"
            dateFormat="yyyy-MM-dd"
            locale={locale}
          />
        </span>
      </h2>

      <DateRangeSelector onChange={handleDateRangeChange} />

      <div className="flex flex-col gap-6">
        {hasPermission("expenses", "analytics") && (
          <div className="w-full">
            <ExpenseSummary expenses={filterExpensesByDate(expenses)} />
          </div>
        )}

        <div className="w-full">
          {loading ? (
            <LoadingComp />
          ) : (
            <UpcomingEventsPage
              events={expenses}
              setEvents={setExpenses}
              mode="expenses"
              selectedEventId={eventId || undefined} // Pass the string ID directly, no need to parse to number
            />
          )}
        </div>
      </div>

      {selectedExpense && (
        <ExpensePopup
          expense={selectedExpense}
          onClose={handlePopupClose}
          onSave={handleSavePopUp}
        />
      )}
    </>
  );
};

export default ExpenseOverview;
