import React, { useState, useEffect } from "react";
import { X } from "lucide-react";
import { Card, CardContent } from "@/components/cards/card";
import { But<PERSON> } from "@/components/ui/button";
import { Location } from "@/lib/types/location";
import { createPortal } from "react-dom";
import useLanguage from "@/hooks/useLanguage";
import EditConfirmationModal from "../Modals/EditConfirmationModal";
import { useContacts } from "@/hooks/useContact";
import { LoadingComp } from "../common/Loading";
import { useLocationServices } from "@/hooks/useLocations";
import { ErrorPopup } from "../common/errorPopUp";
import { SuccessPopup } from "../common/successPopUp";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { CreateLocationTypePayload } from "@/lib/locations";
import {
  LocationFormProps,
  OwnershipShare,
  LocationType,
} from "./LocationFormComponents/types";
import GeneralTab from "./LocationFormComponents/GeneralTab";
import OwnershipTab from "./LocationFormComponents/OwnershipTab";
import { DataImporter } from "../Importer/DataImporter";
import OwnershipPopup from "./LocationFormComponents/PartnerShipsPopup";
const LocationForm: React.FC<LocationFormProps> = ({
  isOpen,
  onClose,
  location,
  onSubmit,
  view = "modal",
}) => {
  const [mounted, setMounted] = useState(false);
  const { t } = useLanguage();
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [activeTab, setActiveTab] = useState("general");
  const [formData, setFormData] = useState<Partial<Location>>({
    name: location?.name || "",
    address: location?.address || "",
    type: location?.type || "",
    status: location?.status || "active",
    capacity: location?.capacity || 0,
    contactPerson: location?.contactPerson || "",
    contactEmail: location?.contactEmail || "",
    contactPhone: location?.contactPhone || "",
    ownedBy: location?.ownedBy || "",
    ourPercentage: location?.ourPercentage || 100,
    sharedWith: location?.sharedWith || [],
    description: location?.description || "",
  });

  // Type related states
  const [typeIdsMapping, setTypeIdsMapping] = useState<Record<string, string>>(
    {},
  );
  const [isImporting, setIsImporting] = useState(false);
  const [selectedTypeId, setSelectedTypeId] = useState<string>("");
  const [locationTypesWithIds, setLocationTypesWithIds] = useState<
    LocationType[]
  >([]);

  // Ownership related states
  const [sharedWith, setSharedWith] = useState<string[]>(
    location?.sharedWith || [],
  );
  const [isOwnedByEgyComm, setIsOwnedByEgyComm] = useState(
    location?.ownedBy === "EgyComm",
  );
  const [isEgyCommPrimaryOwner, setIsEgyCommPrimaryOwner] = useState(true);
  const [ownershipShares, setOwnershipShares] = useState<OwnershipShare[]>([]);
  const [percentageError, setPercentageError] = useState("");
  const [contactOptions, setContactOptions] = useState<string[]>([]);

  // UI states
  const [showSuccessPopup, setShowSuccessPopup] = useState(false);
  const [showErrorPopup, setShowErrorPopup] = useState(false);
  const [message, setMessage] = useState("");
  const [loading, setLoading] = useState(false);

  const locationServices = useLocationServices();
  const queryClient = useQueryClient();
  const { data, isLoading, isError } = useContacts();

  // Calculate total percentage from ownership shares
  const calculateTotalPercentage = () => {
    return ownershipShares.reduce((sum, share) => sum + share.percentage, 0);
  };

  // Get total percentage
  const totalPercentage = calculateTotalPercentage();
  const handleImportedLocations = (imported: Partial<Location>[]) => {
    imported.forEach(onSubmit);
  };

  // Calculate combined total percentage
  const combinedTotalPercentage =
    totalPercentage + (formData.ourPercentage || 0);

  // Fetch location types
  const { data: locationTypesList = [], isLoading: typesLoading } = useQuery({
    queryKey: ["locationTypes"],
    queryFn: async () => {
      try {
        const typesDataWithIds = await locationServices.getAllLocationTypes();

        if (Array.isArray(typesDataWithIds) && typesDataWithIds.length > 0) {
          setLocationTypesWithIds(typesDataWithIds);

          const mapping: Record<string, string> = {};
          typesDataWithIds.forEach((type) => {
            if (type.name && type.id) {
              mapping[type.name] = type.id;
            }
          });
          setTypeIdsMapping(mapping);

          if (location?.type) {
            const typeObject = typesDataWithIds.find(
              (t) => t.name === location.type,
            );
            if (typeObject) {
              setSelectedTypeId(typeObject.id);
            }
          }

          return typesDataWithIds.map((type) => type.name);
        }
        return [];
      } catch (error) {
        console.error("Error fetching location types:", error);
        return [];
      }
    },
  });

  // Create location type mutation
  const createTypeMutation = useMutation({
    mutationFn: async (payload: CreateLocationTypePayload) => {
      return await locationServices.createLocationType(payload);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["locationTypes"] });
    },
    onError: (error) => {
      console.error("Error creating location type:", error);
    },
  });

  // Initialize contact options
  useEffect(() => {
    if (data && data.contacts) {
      const contacts = data.contacts.map((contact) => contact.name);
      setContactOptions(contacts);
    }
  }, [data]);

  // Handle body scroll when modal is open/closed
  useEffect(() => {
    setMounted(true);
    if (isOpen && view === "modal") {
      document.body.style.overflow = "hidden";
      window.scrollTo({ top: 0, behavior: "smooth" });
    }
    return () => {
      document.body.style.overflow = "unset";
    };
  }, [isOpen, view]);

  // Initialize form data from the location prop
  useEffect(() => {
    if (location) {
      setFormData({
        name: location.name,
        address: location.address,
        type: location.type,
        status: location.status,
        capacity: location.capacity,
        ownedBy: location.ownedBy,
        ourPercentage: location.ourPercentage,
        sharedWith: location.sharedWith,
        description: location.description || "",
      });
      setSharedWith(location.sharedWith || []);
    }
  }, [location]);

  // Initialize ownership shares
  useEffect(() => {
    if (location) {
      // Check if EgyComm is the primary owner by checking if primaryOwner name is "SarayVera"
      const isEgyCommPrimary = Boolean(
        location.primaryOwner?.name === "SarayVera" || location.areWeOwner,
      );

      setIsOwnedByEgyComm(isEgyCommPrimary);
      setIsEgyCommPrimaryOwner(isEgyCommPrimary);

      const shares: OwnershipShare[] = [];

      // If EgyComm is not the primary owner, we need to find who is
      if (!isEgyCommPrimary && location.primaryOwner) {
        // Set the primary owner in formData
        handleChange("ownedBy", location.primaryOwner.name);

        // Add primary owner to shares if they're not EgyComm
        if (location.primaryOwner.name !== "SarayVera") {
          shares.push({
            name: location.primaryOwner.name,
            percentage:
              location.primaryOwner.percentage || 100 - location.ourPercentage,
          });
        }
      } else {
        // EgyComm is primary owner
        handleChange("ownedBy", "EgyComm");
      }

      // Add other ownership shares (excluding EgyComm/SarayVera and the primary owner)
      if (location.ownershipShares && Array.isArray(location.ownershipShares)) {
        location.ownershipShares.forEach((share) => {
          // Skip if this is SarayVera (EgyComm) or if it's already added as primary owner
          if (
            share.name !== "SarayVera" &&
            share.name !== location.primaryOwner?.name
          ) {
            shares.push({
              name: share.name,
              percentage: share.percentage,
            });
          }
        });
      }

      // Handle sharedWith array - filter out SarayVera as it represents our company
      const filteredSharedWith = (location.sharedWith || []).filter(
        (partner) => partner !== "SarayVera",
      );
      setSharedWith(filteredSharedWith);

      // Update formData sharedWith
      handleChange("sharedWith", filteredSharedWith);

      setOwnershipShares(shares);
    } else {
      setIsOwnedByEgyComm(true);
      setIsEgyCommPrimaryOwner(true);
      setOwnershipShares([]);
    }
  }, [location]);

  // Form field change handler
  const handleChange = (field: keyof Location, value: any) => {
    setFormData((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  // Add custom location type handler
  const handleAddCustomType = async (customType: string) => {
    if (customType.trim()) {
      try {
        const payload: CreateLocationTypePayload = {
          name: customType.trim().toLowerCase(),
        };

        await createTypeMutation.mutateAsync(payload);
        handleChange("type", customType.trim().toLowerCase());
      } catch (error) {
        // Error is handled by the mutation's onError
      }
    }
  };

  // Validate percentages
  const validatePercentages = () => {
    // Use a small epsilon value for floating point comparison
    const epsilon = 0.01;
    if (Math.abs(combinedTotalPercentage - 100) > epsilon) {
      setPercentageError(t("totalPercentageMustBe100"));
      return false;
    }
    setPercentageError("");
    return true;
  };

  // Owner selection handler
  const handleOwnedBySelect = (owner: string) => {
    const isEgyComm = owner === "EgyComm";
    setIsOwnedByEgyComm(isEgyComm);
    setIsEgyCommPrimaryOwner(isEgyComm);
    handleChange("ownedBy", owner);

    if (isEgyComm) {
      setOwnershipShares([]);
    } else {
      const newShares = [];
      newShares.push({
        name: owner,
        percentage: 50,
      });
      setOwnershipShares(newShares);
    }

    setFormData((prev) => ({
      ...prev,
      ownedBy: owner,
      ourPercentage: isEgyComm ? 100 : 50,
      sharedWith: isEgyComm ? [] : [owner],
    }));
  };

  // Form validation
  const isFormValid = () => {
    const requiredFields = ["name", "type", "address", "capacity"];
    const areFieldsFilled = requiredFields.every(
      (field) => formData[field as keyof Location],
    );

    // Use a small epsilon value for floating point comparison
    const epsilon = 0.01;
    const isPercentageValid =
      Math.abs(combinedTotalPercentage - 100) <= epsilon;

    return areFieldsFilled && isPercentageValid;
  };

  // Submit form handler
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);

    if (!validatePercentages() || !isFormValid()) {
      setLoading(false);
      return;
    }

    try {
      const ownership_shares = [];

      for (const share of ownershipShares) {
        const contactId = data?.contacts
          .find((contact) => contact.name === share.name)
          ?.id.toString();

        if (contactId) {
          const isPrimaryOwner =
            !isEgyCommPrimaryOwner && share.name === formData.ownedBy;

          ownership_shares.push({
            contact_id: contactId,
            percentage: share.percentage,
            is_primary_owner: isPrimaryOwner,
          });
        }
      }

      const payload = {
        name: formData.name || "",
        description: formData.description || "",
        address: formData.address || "",
        type_ids: selectedTypeId ? [selectedTypeId] : [],
        is_active: formData.status === "active",
        is_reserved: false,
        capacity: formData.capacity || 0,
        our_percentage: formData.ourPercentage || 0,
        ownership_shares: ownership_shares,
      };

      try {
        if (location) {
          await locationServices.updateLocation(location.id, payload);
        } else {
          await locationServices.createLocation(payload);
        }

        setShowSuccessPopup(true);
        setMessage(t("locationSavedSuccessfully"));
        setTimeout(() => {
          window.location.reload();
        }, 2000);

        onClose();
      } catch (axiosError: any) {
        console.error("API Error:", axiosError.response);
        throw axiosError;
      }
    } catch (error: any) {
      console.error("Error submitting the form:", error);
      setLoading(false);
      setShowErrorPopup(true);
      setMessage(error.response?.data?.message || t("errorSavingLocation"));
    }
  };

  const renderForm = () => (
    <form onSubmit={handleSubmit} className="space-y-6">
      {showSuccessPopup && (
        <SuccessPopup
          message={message}
          onClose={() => setShowSuccessPopup(false)}
        />
      )}
      {showErrorPopup && (
        <ErrorPopup
          message={message}
          onClose={() => setShowErrorPopup(false)}
        />
      )}
            <div className="mb-6 flex items-center justify-between">
        <div>
          <h3 className="text-lg font-semibold">{t("importLocations")}</h3>
          <p className="text-sm text-gray-500 dark:text-gray-400">
            {t("importLocationsDescription")}
          </p>
        </div>
        <button
          type="button"
          onClick={() => setIsImporting((prev) => !prev)}
          className={`rounded-lg px-4 py-2 text-white ${
            isImporting
              ? "bg-red-600 hover:bg-red-700"
              : "bg-blue-600 hover:bg-blue-700"
          }`}
        >
          {isImporting ? t("cancelImport") : t("startImport")}
        </button>
      </div>
        {isImporting ? (
        <div className="my-6">
          <h3 className="text-lg font-semibold">{t("Import Locations")}</h3>
          <DataImporter<Partial<Location>>
            templateFields={[
              "name",
              "address",
              "type",
              "status",
              "capacity",
            ]}
            onImport={handleImportedLocations}
            manualDropdowns={[
              { field: "locationType", options: ["warehouse", "office", "store"] },
            ]}
            manualModals={[
              {
                field: "OwnerShip",
                component: OwnershipPopup,
              }
            ]}
          />
        </div>
      ) : (
        <>
      <div className="flex space-x-4 border-b pb-4">
        <button
          type="button"
          onClick={() => setActiveTab("general")}
          className={`pb-2 font-medium ${
            activeTab === "general"
              ? "border-b-2 border-blue-600 text-blue-600"
              : "text-gray-500 hover:text-gray-700"
          }`}
        >
          {t("generalInfo")}
        </button>
        <button
          type="button"
          onClick={() => setActiveTab("ownership")}
          className={`pb-2 font-medium ${
            activeTab === "ownership"
              ? "border-b-2 border-blue-600 text-blue-600"
              : "text-gray-500 hover:text-gray-700"
          }`}
        >
          {t("ownershipFinancial")}
        </button>
      </div>

      {activeTab === "general" && (
        <GeneralTab
          formData={formData}
          handleChange={handleChange}
          locationTypesWithIds={locationTypesWithIds}
          selectedTypeId={selectedTypeId}
          setSelectedTypeId={setSelectedTypeId}
          typesLoading={typesLoading}
          t={t}
        />
      )}

      {activeTab === "ownership" && (
        <OwnershipTab
          formData={formData}
          handleChange={handleChange}
          ownershipShares={ownershipShares}
          setOwnershipShares={setOwnershipShares}
          isEgyCommPrimaryOwner={isEgyCommPrimaryOwner}
          setIsEgyCommPrimaryOwner={setIsEgyCommPrimaryOwner}
          isOwnedByEgyComm={isOwnedByEgyComm}
          setIsOwnedByEgyComm={setIsOwnedByEgyComm}
          handleOwnedBySelect={handleOwnedBySelect}
          contactOptions={contactOptions}
          sharedWith={sharedWith}
          t={t}
          totalPercentage={totalPercentage}
          percentageError={percentageError}
          validatePercentages={validatePercentages}
        />
      )}
      </>
      )}
      <div className="flex justify-end gap-4 pt-4">
        <Button type="button" variant="outline" onClick={onClose}>
          {t("cancel")}
        </Button>
        <Button
          type="submit"
          variant="default"
          className="bg-blue-600 text-white hover:bg-blue-700 dark:bg-blue-700 dark:hover:bg-blue-800"
          disabled={!isFormValid()}
        >
          {location ? t("saveChanges") : t("addLocation")}
        </Button>
      </div>
      {location && (
        <EditConfirmationModal
          isOpen={isEditModalOpen}
          onClose={() => setIsEditModalOpen(false)}
          onConfirm={() => {
            onSubmit(formData);
            setIsEditModalOpen(false);
            onClose();
          }}
          title={t("editLocation")}
          itemName={formData.name || ""}
        />
      )}
    </form>
  );

  if (!mounted || !isOpen) return null;

  // Modal view
  if (isLoading || loading) return <LoadingComp />;
  if (view === "modal") {
    return createPortal(
      <div className="fixed inset-0 z-[9999] flex items-center justify-center">
        <div
          className="fixed inset-0 bg-black/50 backdrop-blur-sm transition-opacity duration-200"
          onClick={onClose}
        />
        <div className="relative">
          <Card className="mx-4 w-full max-w-2xl bg-white shadow-xl dark:bg-gray-900">
            <CardContent className="max-h-[90vh] overflow-y-auto p-6">
              <div className="mb-6 flex items-center justify-between">
                <h2 className="text-xl font-bold text-gray-900 dark:text-white">
                  {location ? t("editLocation") : t("addNewLocation")}
                </h2>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={onClose}
                  className="h-8 w-8 rounded-full p-0 hover:bg-gray-100 dark:hover:bg-gray-800"
                >
                  <X className="h-4 w-4" />
                </Button>
              </div>
              {renderForm()}
            </CardContent>
          </Card>
        </div>
      </div>,
      document.body,
    );
  }

  // Sidebar view (for inline editing)
  return (
    <div className="rounded-lg bg-white p-6 shadow-md dark:bg-gray-900">
      <div className="mb-6 flex items-center justify-between">
        <h2 className="text-xl font-bold text-gray-900 dark:text-white">
          {location ? t("editLocation") : t("addNewLocation")}
        </h2>
        <Button
          variant="ghost"
          size="sm"
          onClick={onClose}
          className="h-8 w-8 rounded-full p-0 hover:bg-gray-100 dark:hover:bg-gray-800"
        >
          <X className="h-4 w-4" />
        </Button>
      </div>
      {renderForm()}
    </div>
  );
};

export default LocationForm;
