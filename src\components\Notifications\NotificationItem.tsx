import React, { useState } from "react";
import { Notification } from "@/lib/types/notification";
import { formatDistanceToNow } from "date-fns";
import useLanguage from "@/hooks/useLanguage";
import { ar } from "date-fns/locale";
import EventStatusModal from "./EventStatusModal";

interface NotificationItemProps {
  notification: Notification;
  onMarkAsRead: (id: string) => void;
  onMarkAsUnread?: (id: string) => void;
  onDelete?: (id: string) => void;
}

const NotificationItem: React.FC<NotificationItemProps> = ({
  notification,
  onMarkAsRead,
  onMarkAsUnread,
  onDelete,
}) => {
  const { t, language } = useLanguage();
  const isRTL = language === "ar";
  const [isModalOpen, setIsModalOpen] = useState(false);

  // Check if this is an event-related notification
  const isEventNotification =
    notification.category === "income_due_date" ||
    notification.category === "expense_due_date";

  // Get the priority color
  const getPriorityColor = (priority: string) => {
    switch (priority.toLowerCase()) {
      case "high":
        return "bg-red-500";
      case "medium":
        return "bg-amber-500";
      case "low":
        return "bg-blue-500";
      default:
        return "bg-gray-500";
    }
  };

  // Format the date to a relative time
  const formatDate = (dateString: string) => {
    try {
      const date = new Date(dateString);
      return formatDistanceToNow(date, {
        addSuffix: true,
        locale: language === "ar" ? ar : undefined,
      });
    } catch (error) {
      console.error("Error formatting date:", error);
      return dateString;
    }
  };
  
  // Handle marking notification as read
  const handleMarkAsRead = (e: React.MouseEvent) => {
    e.stopPropagation();
    onMarkAsRead(notification.id);
  };

  // Handle marking notification as unread
  const handleMarkAsUnread = (e: React.MouseEvent) => {
    e.stopPropagation();
    if (onMarkAsUnread) onMarkAsUnread(notification.id);
  };

  // Handle deleting notification
  const handleDelete = (e: React.MouseEvent) => {
    e.stopPropagation();
    if (onDelete) onDelete(notification.id);
  };

  // Handle event status update
  const handleEventClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    if (isEventNotification) {
      setIsModalOpen(true);
    }
  };

  return (
    <>
      <div
        className={`border-b border-gray-200 p-4 last:border-b-0 dark:border-gray-700
                    ${
                      !notification.is_read
                        ? "bg-blue-50/30 dark:bg-blue-900/10"
                        : "bg-white dark:bg-boxdark"
                    }
                    cursor-pointer transition-colors duration-200 hover:bg-gray-50 dark:hover:bg-meta-4
                    ${isRTL ? "rtl" : "ltr"}`}
        onClick={!notification.is_read ? handleMarkAsRead : undefined}
      >
        <div
          className={`flex gap-3 ${isRTL ? "flex-row-reverse text-right" : "text-left"}`}
        >
          {/* Priority indicator */}
          <div className="flex-shrink-0">
            <div
              className={`h-2 w-2 rounded-full ${getPriorityColor(notification.priority)} mt-2`}
            ></div>
          </div>

          {/* Content */}
          <div className="flex-grow">
            <div className="flex items-center gap-2">
              <h4 className="text-sm font-medium text-gray-900 dark:text-white">
                {language === "ar"
                  ? notification.title_ar || notification.title
                  : notification.title}
              </h4>
              {isEventNotification && (
                <button
                  onClick={handleEventClick}
                  className="inline-flex items-center rounded-full bg-primary/10 px-2 py-1 text-xs font-medium text-primary transition-colors hover:bg-primary/20"
                  title={
                    notification.category === "income_due_date"
                      ? t("updateIncomeStatus")
                      : t("updateExpenseStatus")
                  }
                >
                  <svg
                    className="mr-1 h-3 w-3"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"
                    />
                  </svg>
                  {t("updateStatus")}
                </button>
              )}
            </div>
            <p className="mt-1 line-clamp-2 text-xs text-gray-500 dark:text-gray-400">
              {language === "ar"
                ? notification.message_ar || notification.message
                : notification.message}
            </p>

            {/* Type and time */}
            <div className="mt-1 flex items-center text-xs text-gray-400 dark:text-gray-500">
              <span className="mr-2 font-medium">{notification.type.name}</span>
              <span>•</span>
              <span className="ml-2">
                {notification.view_date
                  ? formatDate(notification.view_date)
                  : t("unknownDate")}
              </span>
            </div>
          </div>

          {/* Actions */}
          <div className="flex flex-shrink-0 gap-1.5 self-start">
            {!notification.is_read ? (
              <button
                className="rounded-full p-1 text-blue-600 hover:bg-blue-100/50 hover:text-blue-800 dark:text-blue-500 dark:hover:bg-blue-900/20 dark:hover:text-blue-400"
                onClick={handleMarkAsRead}
                title={t("markAsRead")}
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="h-4 w-4"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M5 13l4 4L19 7"
                  />
                </svg>
              </button>
            ) : (
              onMarkAsUnread && (
                <button
                  className="rounded-full p-1 text-gray-500 hover:bg-blue-100/50 hover:text-blue-700 dark:text-gray-400 dark:hover:bg-blue-900/20 dark:hover:text-blue-300"
                  onClick={handleMarkAsUnread}
                  title={t("markAsUnread")}
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="h-4 w-4"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"
                    />
                  </svg>
                </button>
              )
            )}

            {onDelete && (
              <button
                className="rounded-full p-1 text-gray-400 hover:bg-red-100/50 hover:text-red-500 dark:text-gray-600 dark:hover:bg-red-900/20 dark:hover:text-red-500"
                onClick={handleDelete}
                title={t("delete")}
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="h-4 w-4"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"
                  />
                </svg>
              </button>
            )}
          </div>
        </div>
      </div>

      {/* Event Status Modal */}
      {isEventNotification && (
        <EventStatusModal
          isOpen={isModalOpen}
          onClose={() => setIsModalOpen(false)}
          category={notification.category}
          category_id={notification.category_id}
          eventTitle={
            language === "ar"
              ? notification.title_ar || notification.title
              : notification.title
          }
        />
      )}
    </>
  );
};

export default NotificationItem;
