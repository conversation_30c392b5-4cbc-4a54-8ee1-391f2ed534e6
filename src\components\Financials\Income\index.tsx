"use client";
import React, { useState, useEffect, useCallback } from "react";
import { useSearchParams } from "next/navigation"; // Import useSearchParams
import Breadcrumb from "@/components/Breadcrumbs/Breadcrumb";
import useLanguage from "@/hooks/useLanguage";
import { EventDetails } from "@/lib/interfaces/finaces";
import IncomeSummary from "./IncomeSummary";
import UpcomingEventsPage from "@/components/Dashboard/UpcommingEventsPage/UpcommingEvents";
import DatePicker from "react-datepicker";
import "react-datepicker/dist/react-datepicker.css";
import { enUS, arSA } from "date-fns/locale";
import { getDateRange, DateRangeSelector } from "@/components/Financials/common/dateRanges"; 
import {LoadingComp} from "@/components/common/Loading";
import { usePermissions } from "@/hooks/usePermissions";
import IncomeServices from "@/lib/income";

const IncomeOverview: React.FC = () => {
    const { t, language } = useLanguage();
    const searchParams = useSearchParams(); // Initialize useSearchParams
    const [incomes, setIncomes] = useState<EventDetails[]>([]);
    const [loading, setLoading] = useState(true);
    // Calculate date range
    const { getIncomes } = IncomeServices();
    const currentDate = new Date();
    const startRangeDate = new Date(currentDate);
    startRangeDate.setDate(currentDate.getDate() - 15);
    const endRangeDate = new Date(currentDate);
    endRangeDate.setDate(currentDate.getDate() + 15);

    const formatDate = (date: Date) => {
        return date.toISOString().split('T')[0];
    };

    useEffect(() => {
        const fetchIncomes = async () => {
            setLoading(true);
            try {
                const result = await getIncomes();
                console.log("Fetched incomes:", result);
                setIncomes(result || []);
            } catch (err) {
                console.error("Failed to fetch incomes:", err);
            } finally {
                setLoading(false);
            }
        };

        fetchIncomes();
    }, []);
    
    const [selectedIncome, setSelectedIncome] = useState<EventDetails | null>(null);
    const [startDate, setStartDate] = useState<Date>(startRangeDate);
    const [endDate, setEndDate] = useState<Date>(endRangeDate);

    const handleEditClick = (id: string) => {
        const income = incomes.find((income) => income.id === id);
        if (income) {
            setSelectedIncome(income);
        }
    };

    const handlePopupClose = () => setSelectedIncome(null);
    const handleSavePopUp = (updatedIncome: EventDetails) => {
        setIncomes((prevIncomes) =>
            prevIncomes.map((income) =>
                income.id === updatedIncome.id ? updatedIncome : income
            )
        );
        handlePopupClose();
    };

    const filterIncomesByDate = (incomes: EventDetails[]) => {
        return incomes.filter(income => {
            const incomeDate = new Date(income.dueDate);
            const start = startDate ? new Date(startDate) : null;
            const end = endDate ? new Date(endDate) : null;

            if (start && end) {
                return incomeDate >= start && incomeDate <= end;
            } else if (start) {
                return incomeDate >= start;
            } else if (end) {
                return incomeDate <= end;
            } else {
                return true;
            }
        });
    };

    function handleSave(updatedIncomes: EventDetails[]): void {
        setIncomes(updatedIncomes);
    }
      const handleDateRangeChange = (range: string) => {
        const { start, end } = getDateRange(range);
        setStartDate(new Date(start));
        setEndDate(new Date(end));
      };

    // Get eventId from URL parameters
    const eventId = searchParams ? searchParams.get("eventId") : null;

    const locale = language === "ar" ? arSA : enUS;
    const {hasPermission , permissionsLoaded } = usePermissions();
      if (!permissionsLoaded) {
        return (
          <div className="fixed top-0 left-0 z-9999 flex h-screen w-full items-center justify-center bg-black dark:bg-boxdark">
            <LoadingComp />
          </div>
        );
        
      }

    return (
        <>
            <h2 className="text-2xl font-bold text-gray-900 dark:text-gray-400 mb-4 flex items-center">
                {t("Income Overview")}
                <span className="text-sm font-normal text-gray-600 ml-2 flex items-center">
                    <span className="mr-1">{t("as of")}</span>
                    <DatePicker
                        selected={startDate}
                        onChange={(date: Date | null) => date && setStartDate(date)}
                        className="border rounded p-2 ml-1 dark:bg-gray-800 dark:text-gray-200 dark:border-gray-600"
                        dateFormat="yyyy-MM-dd"
                        locale={locale}
                    />
                    <span className="mx-1">{t("to")}</span>
                    <DatePicker
                        selected={endDate}
                        onChange={(date: Date | null) => date && setEndDate(date)}
                        className="border rounded p-2 ml-1 dark:bg-gray-800 dark:text-gray-200 dark:border-gray-600"
                        dateFormat="yyyy-MM-dd"
                        locale={locale}
                    />
                </span>
            </h2>
            <DateRangeSelector onChange={handleDateRangeChange} />

            <div className="flex flex-col gap-6">
                {hasPermission("income" , "analytics") && (
                <div className="w-full">
                    <IncomeSummary incomes={filterIncomesByDate(incomes)} />
                </div>
                )}
                {loading ? (
                    <div className="flex justify-center items-center h-screen">
                        <LoadingComp />
                    </div>
                ) : (
                    <div className="w-full">
                        <UpcomingEventsPage
                            events={filterIncomesByDate(incomes)}
                            setEvents={setIncomes}
                            mode="income"
                            selectedEventId={eventId || undefined} // Pass the string ID directly
                        />
                    </div>
                )}

            </div>
        </>
    );
};

export default IncomeOverview;