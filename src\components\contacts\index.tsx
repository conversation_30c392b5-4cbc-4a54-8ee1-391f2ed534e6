"use client";

import React, { useState } from "react";
import { Plus, Users, UserPlus } from "lucide-react";
import { Card, CardContent } from "../cards/card";
import ContactForm from "./ContactForm";
import ContactList from "./ContactList";
import ContactDetails from "./ContactDetails";
import InlineEditForm from "./InlineEditForm";
import Breadcrumb from "../Breadcrumbs/Breadcrumb";
import { Contact } from "@/lib/types/contacts";
import useLanguage from "@/hooks/useLanguage";
import { useContactServices } from "@/hooks/useContact";
import { LoadingComp } from "../common/Loading";
import { SuccessPopup } from "../common/successPopUp";
import { ErrorPopup } from "../common/errorPopUp";

const ContactPage = () => {
  const [selectedContact, setSelectedContact] = useState<Contact | null>(null);
  const [isFormOpen, setIsFormOpen] = useState(false);
  const [isEditing, setIsEditing] = useState(false);
  const { t } = useLanguage();
  const [isLoading, setIsLoading] = useState(false);
  const contactService = useContactServices();
  const [message, setMessage] = useState("");
  const [showSuccessPopup, setShowSuccessPopup] = useState(false);
  const [showErrorPopup, setShowErrorPopup] = useState(false);


    if (isLoading) {
      return (
        <div className="flex items-center justify-center h-full">
          <LoadingComp />
        </div>
      );
    }
  const handleSaveContact = async (data: Partial<Contact>) => {
    setIsLoading(true);
    try {
      if (isEditing && selectedContact) {
        console.log("Updating contact:", selectedContact.id, data);
        
        const updated = await contactService.updateContact(selectedContact.id, {
          name: data.name,
          email: data.email,
          phone: data.phone,
          address: data.address,
          company: data.company,
          type: data.type,
        });
        setSelectedContact({
          id: String(updated.contact_id),
          name: updated.name,
          email: updated.email,
          phone: updated.phone,
          address: updated.address,
          company: updated.company,
          type: updated.type?.filter((t: string): t is "client" | "agency" | "other" =>
            ["client", "agency", "other"].includes(t)
          ),
          sharedLocations: updated.sharedLocations ? updated.sharedLocations : [],
          ownedLocations: [],
          createdAt: updated.createdAt ?? "",
          createdBy: updated.createdBy ?? "",
          balance: updated.balance ?? 0,
        });
      } else {
        const created = await contactService.createContact({
          name: data.name ?? "",
          email: data.email ?? "",
          phone: data.phone ?? "",
          address: data.address ?? "",
          company: data.company ?? "",
          type: data.type ?? [],
        });
        setSelectedContact({
          id: String(created.contact_id),
          name: created.name,
          email: created.email,
          phone: created.phone,
          address: created.address,
          company: created.company,
          type: created.type?.filter((t: string): t is "client" | "agency" | "other" =>
            ["client", "agency", "other"].includes(t)
          ),
          sharedLocations: created.sharedLocations ? created.sharedLocations : [],
          ownedLocations: created.ownedLocations?.map((location) => ({
            id: location.id,
            name: location.name,
            address: location.address ?? "", // Ensure address is provided
            capacity: location.capacity ?? 0, // Ensure capacity is provided
            is_active: location.is_active ?? false, // Ensure is_active is provided
          })) ?? [],
          createdAt: created.createdAt ?? "",
          createdBy: created.createdBy ?? "",
          balance: created.balance ?? 0,
        });
      }
      setIsLoading(false);
      setIsFormOpen(false);
      setIsEditing(false);
      setMessage(t("contactSavedSuccessfully"));
      setShowSuccessPopup(true);
      // set time out and refresh the contact list
      setTimeout(() => {
        window.location.reload();
      }, 3000);
    } catch (error) {
      console.error("Failed to save contact:", error);
      setIsLoading(false);
      setShowErrorPopup(true);
      setMessage(t("errorSavingContact"));
    }
  };

return (
  <>
    {showSuccessPopup && (
      <SuccessPopup
        message={message}
        onClose={() => setShowSuccessPopup(false)}
      />
    )}
    {showErrorPopup && (
      <ErrorPopup
        message={message}
        onClose={() => setShowErrorPopup(false)}
      />
    )}

    {selectedContact && !isEditing ? (
      // Fullscreen Contact Details View
      <div className="min-h-screen p-6 bg-gray-50 dark:bg-boxdark">
        <ContactDetails
          contact={selectedContact}
          onEdit={() => {
            setIsEditing(true);
            setIsFormOpen(true);
          }}
          onDelete={() => {
            setTimeout(() => {
              window.location.reload();
            }
            , 2000);

            setSelectedContact(null);
          }}
          onClose={() => setSelectedContact(null)} // Or toggle a view state

        />
      </div>
    ) : (
      // Full Contact Management UI
      <>
        <Breadcrumb pageName={t("contacts")} />
        <div className="p-4 min-h-screen bg-gray-50 dark:bg-boxdark">
          <div className="mb-4">
            <div className="flex items-center gap-3">
              <Users size={24} className="text-blue-600" />
              <h1 className="text-2xl font-bold text-gray-900 dark:text-gray-100">
                {t("contactsManagement")}
              </h1>
            </div>
          </div>

          {!isFormOpen && (
            <div className="mb-6">
              <Card className="w-full bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 border-blue-100 dark:border-blue-800/50">
                <CardContent className="py-6">
                  <div className="flex flex-col md:flex-row items-center justify-between">
                    <div className="mb-4 md:mb-0">
                      <h2 className="text-xl font-bold text-blue-800 dark:text-blue-300">
                        {t("addNewContact")}
                      </h2>
                      <p className="text-blue-600 dark:text-blue-400 mt-1">
                        {t("createNewContactDescription")}
                      </p>
                    </div>
                    <button
                      onClick={() => {
                        setIsFormOpen(true);
                        setIsEditing(false);
                      }}
                      className="flex items-center justify-center gap-3 bg-blue-600 text-white px-8 py-3 rounded-lg hover:bg-blue-700 transition-colors text-lg font-medium shadow-md hover:shadow-lg w-full md:w-auto"
                    >
                      <Plus size={22} />
                      {t("addContact")}
                    </button>
                  </div>
                </CardContent>
              </Card>
            </div>
          )}

          {isFormOpen && !isEditing && (
            <div className="mb-6">
              <Card className="w-full bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 border-blue-100 dark:border-blue-800/50 shadow-md">
                <CardContent className="p-6">
                  <div className="mb-4">
                    <h2 className="text-2xl font-bold text-blue-800 dark:text-blue-300 text-center">
                      {t("addNewContact")}
                    </h2>
                    <p className="text-blue-600 dark:text-blue-400 mt-1 text-center">
                      {t("fillContactDetails")}
                    </p>
                  </div>
                  <div className="max-w-4xl mx-auto">
                    <ContactForm
                      isOpen={isFormOpen}
                      onClose={() => setIsFormOpen(false)}
                      onSubmit={handleSaveContact}
                      view="sidebar"
                    />
                  </div>
                </CardContent>
              </Card>
            </div>
          )}

          <div className="mb-6">
            {selectedContact && isEditing ? (
              <InlineEditForm
                contact={selectedContact}
                onSave={handleSaveContact}
                onCancel={() => setIsEditing(false)}
              />
            ) : (
              <Card className="w-full flex items-center justify-center dark:bg-gray-800 dark:border-gray-700">
                <CardContent className="text-center py-12">
                  <UserPlus size={48} className="mx-auto mb-4 text-gray-400" />
                  <p className="text-gray-500 dark:text-gray-400">
                    {t("selectAContactToViewDetails")}
                  </p>
                </CardContent>
              </Card>
            )}
          </div>

          <div>
            <ContactList
              onSelect={(contact: Contact) => setSelectedContact(contact)}
              selectedId={selectedContact?.id}
            />
          </div>
        </div>
      </>
    )}
  </>
);

};

export default ContactPage;
