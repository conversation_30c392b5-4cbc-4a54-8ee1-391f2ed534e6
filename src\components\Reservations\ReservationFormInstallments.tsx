import React, { useState, useEffect } from "react";
import { ReservationFormData } from "@/lib/interfaces/reservation";
import { EventDetails } from "@/lib/interfaces/finaces";
import {
  FaPlus,
  FaTrash,
  FaInfoCircle,
  FaCheck,
  FaChevronDown,
} from "react-icons/fa";
import ReservationServices, {
  ReservationEventsApiResponse,
} from "@/lib/reservations";
import IncomeServices from "@/lib/income";
import ExpenseServices from "@/lib/expenses";
import { useReservationEvents } from "@/hooks/useReservations";

interface ReservationFormInstallmentsProps {
  formData: ReservationFormData;
  handleInputChange: (
    e: React.ChangeEvent<
      HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement
    >,
  ) => void;
  handleAddInstallment: () => void;
  handleRemoveInstallment: (index: number) => void;
  handleInstallmentChange: (index: number, field: string, value: any) => void;
  generateEqualInstallments: () => void;
  t: (key: string) => string;
  language?: string;
  reservationId?: string; // For existing reservations
}

const ReservationFormInstallments: React.FC<
  ReservationFormInstallmentsProps
> = ({
  formData,
  handleInputChange,
  handleAddInstallment,
  handleRemoveInstallment,
  handleInstallmentChange,
  generateEqualInstallments,
  t,
  language = "en",
  reservationId,
}) => {
  // Add state for tabs and financial data display
  const [activeTab, setActiveTab] = useState<"installments" | "financialData">(
    "installments",
  );
  const [activeEventType, setActiveEventType] = useState<"income" | "expense">(
    "income",
  );
  const [incomeData, setIncomeData] = useState<EventDetails[]>([]);
  const [expenseData, setExpenseData] = useState<EventDetails[]>([]);
  const [dropdownOpenId, setDropdownOpenId] = useState<string | null>(null);
  const [showActualAmount, setShowActualAmount] = useState(false);

  // Use the new hook for fetching reservation events
  const {
    data: reservationEventsData,
    isLoading: isLoadingFinancialData,
    isError: hasFinancialDataError,
    error: financialDataError,
    refetch: refetchFinancialData,
  } = useReservationEvents(reservationId);

  // Services
  const ReservationService = ReservationServices();

  // Status options for dropdown
  const statusOptions = [
    "completed",
    "pending",
    "cancelled",
    "upcoming",
    "overdue",
  ];

  // Update financial data when hook data changes
  useEffect(() => {
    if (reservationEventsData) {
      console.log("Fetched financial data:", reservationEventsData);

      if (
        reservationEventsData.incomes &&
        Array.isArray(reservationEventsData.incomes)
      ) {
        const typedIncomes = reservationEventsData.incomes.map(
          (income: any) => ({
            id: income.id || `income-${Date.now()}-${Math.random()}`,
            title: income.title || "",
            amount: Number(income.amount) || 0,
            dueDate: income.dueDate || income.due_date || "",
            status: income.status || "upcoming",
            actual_amount: income.actual_amount,
            type: income.type || "Payment",
          }),
        );
        setIncomeData(typedIncomes as EventDetails[]);
      }

      if (
        reservationEventsData.expenses &&
        Array.isArray(reservationEventsData.expenses)
      ) {
        const typedExpenses = reservationEventsData.expenses.map(
          (expense: any) => ({
            id: expense.id || `expense-${Date.now()}-${Math.random()}`,
            title: expense.title || "",
            amount: Number(expense.amount) || 0,
            dueDate: expense.dueDate || expense.due_date || "",
            status: expense.status || "upcoming",
            actual_amount: expense.actual_amount,
            type: expense.type || "Expense",
          }),
        );
        setExpenseData(typedExpenses as EventDetails[]);
      }
    }
  }, [reservationEventsData]);

  // Handle event status change with optimistic updates
  const handleEventStatusChange = async (
    eventId: string,
    newStatus: string,
    type: "income" | "expense",
  ) => {
    if (!eventId) return;

    try {
      if (type === "income") {
        await IncomeServices().updateIncomeStatus(eventId, newStatus);
        setIncomeData((prev) =>
          prev.map((ev) =>
            ev.id === eventId
              ? { ...ev, status: newStatus as EventDetails["status"] }
              : ev,
          ),
        );
      } else {
        await ExpenseServices().updateExpenseStatus(eventId, newStatus);
        setExpenseData((prev) =>
          prev.map((ev) =>
            ev.id === eventId
              ? { ...ev, status: newStatus as EventDetails["status"] }
              : ev,
          ),
        );
      }

      // Refetch data to ensure consistency
      if (refetchFinancialData) {
        refetchFinancialData();
      }
    } catch (err) {
      console.error("Failed to update status:", err);
    }
  };

  // Helper functions for displaying data
  const formatDate = (dateString: string | undefined) => {
    return dateString
      ? new Date(dateString).toLocaleDateString(
          language === "ar" ? "ar-EG" : "en-US",
        )
      : "N/A";
  };

  const formatCurrency = (amount: number | undefined) => {
    return amount !== undefined && amount !== null
      ? new Intl.NumberFormat(language === "ar" ? "ar-EG" : "en-US", {
          style: "currency",
          currency: "EGP",
          minimumFractionDigits: 0,
        }).format(amount)
      : "N/A";
  };

  const getStatusBadgeClass = (status: string) => {
    switch (status) {
      case "active":
        return "bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-100";
      case "pending":
        return "bg-yellow-100 text-yellow-800 dark:bg-yellow-800 dark:text-yellow-100";
      case "completed":
        return "bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-100";
      case "cancelled":
        return "bg-red-100 text-red-800 dark:bg-red-800 dark:text-red-100";
      case "upcoming":
        return "bg-indigo-100 text-indigo-800 dark:bg-indigo-800 dark:text-indigo-100";
      case "overdue":
        return "bg-orange-100 text-orange-800 dark:bg-orange-800 dark:text-orange-100";
      default:
        return "bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-100";
    }
  };

  // Calculate financial summaries
  const totalIncome = incomeData.reduce(
    (sum, event) => sum + (Number(event.amount) || 0),
    0,
  );

  const paidIncome = incomeData
    .filter((event) => event.status === "completed")
    .reduce((sum, event) => sum + (Number(event.amount) || 0), 0);

  const totalExpense = expenseData.reduce(
    (sum, event) => sum + (Number(event.amount) || 0),
    0,
  );

  const paidExpense = expenseData
    .filter((event) => event.status === "completed")
    .reduce((sum, event) => sum + (Number(event.amount) || 0), 0);

  return (
    <div className="rounded-lg border border-gray-200 bg-gray-50 p-4 dark:border-gray-700 dark:bg-gray-900 sm:p-6">
      {/* Add tab navigation for switching between installments and financial data */}
      <div className="mb-6 border-b border-gray-200 dark:border-gray-700">
        <nav className="flex space-x-4" aria-label="Tabs">
          <button
            onClick={() => setActiveTab("installments")}
            className={`px-4 py-2 text-sm font-medium ${
              activeTab === "installments"
                ? "border-b-2 border-blue-500 text-blue-600 dark:text-blue-400"
                : "text-gray-500 hover:text-gray-700 dark:text-gray-400"
            }`}
          >
            {t("Installments Input")}
          </button>
          {reservationId && (
            <button
              onClick={() => setActiveTab("financialData")}
              className={`px-4 py-2 text-sm font-medium ${
                activeTab === "financialData"
                  ? "border-b-2 border-blue-500 text-blue-600 dark:text-blue-400"
                  : "text-gray-500 hover:text-gray-700 dark:text-gray-400"
              }`}
            >
              {t("Financial Data")}
            </button>
          )}
        </nav>
      </div>

      {activeTab === "installments" && (
        <>
          {/* Existing installment input UI */}
          <div className="mb-4 flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
            <h3 className="text-lg font-semibold text-gray-800 dark:text-gray-200">
              {t("Payment Installments")}
            </h3>
            <button
              type="button"
              onClick={handleAddInstallment}
              className="flex w-full items-center justify-center gap-2 rounded-md bg-blue-600 px-4 py-2 text-sm font-semibold text-white shadow-sm hover:bg-blue-500 sm:w-auto"
            >
              <FaPlus className="h-4 w-4" />
              {t("Add Installment")}
            </button>
          </div>

          {/* Quick Generator */}
          <div className="mb-6 rounded-lg border border-gray-200 bg-white p-4 dark:border-gray-600 dark:bg-gray-800">
            <h4 className="mb-3 text-sm font-medium text-gray-900 dark:text-white">
              {t("Quick Generate")}
            </h4>
            <div className="flex flex-col gap-4 sm:flex-row sm:items-end">
              <div className="flex-1">
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                  {t("Number of Installments")}
                </label>
                <input
                  type="number"
                  name="installmentCount"
                  value={formData.installmentCount}
                  onChange={handleInputChange}
                  className="mt-1 block w-full rounded-md border-gray-300 px-3 py-2 shadow-sm focus:border-blue-500 focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-700 dark:text-gray-200 sm:text-sm"
                  min="1"
                />
              </div>
              <button
                type="button"
                onClick={() => {
                  if (
                    formData.totalAmount <= 0 ||
                    formData.installmentCount <= 0
                  ) {
                    alert(
                      t(
                        "Total amount and installment count must be greater than zero",
                      ),
                    );
                    return;
                  }
                  generateEqualInstallments();
                }}
                className="flex w-full items-center justify-center gap-2 rounded-md bg-green-600 px-4 py-2 text-sm font-semibold text-white shadow-sm hover:bg-green-500 sm:w-auto"
              >
                <FaCheck className="h-4 w-4" />
                {t("Generate Equal")}
              </button>
            </div>
          </div>

          {/* Installments List */}
          {formData.installments.length > 0 ? (
            <div className="space-y-4">
              {formData.installments.map((installment, index) => (
                <div
                  key={index}
                  className="rounded-lg border border-gray-200 bg-white p-4 dark:border-gray-600 dark:bg-gray-800"
                >
                  <div className="mb-3 flex items-center justify-between">
                    <h5 className="font-medium text-gray-900 dark:text-white">
                      {t("Installment")} #{index + 1}
                    </h5>
                    <button
                      type="button"
                      onClick={() => handleRemoveInstallment(index)}
                      className="text-red-600 hover:text-red-800 dark:text-red-400 dark:hover:text-red-300"
                    >
                      <FaTrash className="h-4 w-4" />
                    </button>
                  </div>

                  <div className="grid gap-4 sm:grid-cols-2 lg:grid-cols-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                        {t("Amount")}
                      </label>
                      <input
                        type="number"
                        value={installment.amount}
                        onChange={(e) =>
                          handleInstallmentChange(
                            index,
                            "amount",
                            e.target.value,
                          )
                        }
                        className="mt-1 block w-full rounded-md border-gray-300 px-3 py-2 shadow-sm focus:border-blue-500 focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-700 dark:text-gray-200 sm:text-sm"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                        {t("Due Date")}
                      </label>
                      <input
                        type="date"
                        value={installment.dueDate}
                        onChange={(e) =>
                          handleInstallmentChange(
                            index,
                            "dueDate",
                            e.target.value,
                          )
                        }
                        className="mt-1 block w-full rounded-md border-gray-300 px-3 py-2 shadow-sm focus:border-blue-500 focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-700 dark:text-gray-200 sm:text-sm"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                        {t("Status")}
                      </label>
                      <select
                        value={installment.status || "upcoming"}
                        onChange={(e) => {
                          const newStatus = e.target.value as
                            | "upcoming"
                            | "pending"
                            | "completed"
                            | "overdue"
                            | "cancelled";

                          handleInstallmentChange(index, "status", newStatus);

                          // If status is not completed, clear received_date
                          if (newStatus !== "completed") {
                            handleInstallmentChange(index, "received_date", "");
                          }
                        }}
                        className="mt-1 block w-full rounded-md border-gray-300 px-3 py-2 shadow-sm focus:border-blue-500 focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-700 dark:text-gray-200 sm:text-sm"
                      >
                        <option value="upcoming">{t("Upcoming")}</option>
                        <option value="pending">{t("Pending")}</option>
                        <option value="completed">{t("Completed")}</option>
                        <option value="overdue">{t("Overdue")}</option>
                        <option value="cancelled">{t("Cancelled")}</option>
                      </select>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                        {t("Priority")}
                      </label>
                      <select
                        value={installment.priority}
                        onChange={(e) =>
                          handleInstallmentChange(
                            index,
                            "priority",
                            e.target.value,
                          )
                        }
                        className="mt-1 block w-full rounded-md border-gray-300 px-3 py-2 shadow-sm focus:border-blue-500 focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-700 dark:text-gray-200 sm:text-sm"
                      >
                        <option value="low">{t("Low")}</option>
                        <option value="medium">{t("Medium")}</option>
                        <option value="high">{t("High")}</option>
                      </select>
                    </div>
                  </div>

                  {/* Always show notification date, conditionally show received date */}
                  <div className="mt-4 grid gap-4 sm:grid-cols-2">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                        {t("Notification Date")}
                      </label>
                      <input
                        type="date"
                        value={installment.notificationDate}
                        onChange={(e) =>
                          handleInstallmentChange(
                            index,
                            "notificationDate",
                            e.target.value,
                          )
                        }
                        className="mt-1 block w-full rounded-md border-gray-300 px-3 py-2 shadow-sm focus:border-blue-500 focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-700 dark:text-gray-200 sm:text-sm"
                      />
                    </div>

                    {installment.status === "completed" && (
                      <div>
                        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                          {t("Received Date")}{" "}
                          <span className="text-red-500">*</span>
                        </label>
                        <input
                          type="date"
                          value={installment.received_date || ""}
                          onChange={(e) =>
                            handleInstallmentChange(
                              index,
                              "received_date",
                              e.target.value,
                            )
                          }
                          className="mt-1 block w-full rounded-md border-gray-300 px-3 py-2 shadow-sm focus:border-blue-500 focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-700 dark:text-gray-200 sm:text-sm"
                          required
                        />
                      </div>
                    )}
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="rounded-lg border-2 border-dashed border-gray-300 p-8 text-center dark:border-gray-600">
              <FaInfoCircle className="mx-auto h-12 w-12 text-gray-400" />
              <h3 className="mt-2 text-sm font-medium text-gray-900 dark:text-white">
                {t("No installments yet")}
              </h3>
              <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
                {t("Add installments to manage payments")}
              </p>
            </div>
          )}
        </>
      )}

      {/* Financial Data Tab (Income/Expense View) */}
      {activeTab === "financialData" && (
        <div>
          <h3 className="mb-3 text-lg font-semibold text-gray-800 dark:text-gray-200 sm:mb-4">
            {t("Income & Expenses")}
          </h3>
          <div className="mb-4 flex gap-2">
            <button
              className={`rounded-t-lg px-4 py-2 text-sm font-medium transition-colors ${
                activeEventType === "income"
                  ? "bg-blue-500 text-white"
                  : "bg-gray-200 text-gray-700 dark:bg-gray-700 dark:text-gray-200"
              }`}
              onClick={() => setActiveEventType("income")}
            >
              {t("Income")}
            </button>
            <button
              className={`rounded-t-lg px-4 py-2 text-sm font-medium transition-colors ${
                activeEventType === "expense"
                  ? "bg-blue-500 text-white"
                  : "bg-gray-200 text-gray-700 dark:bg-gray-700 dark:text-gray-200"
              }`}
              onClick={() => setActiveEventType("expense")}
            >
              {t("Expense")}
            </button>
          </div>

          {isLoadingFinancialData ? (
            <div className="flex items-center justify-center p-8">
              <div className="h-12 w-12 animate-spin rounded-full border-b-2 border-primary"></div>
            </div>
          ) : hasFinancialDataError ? (
            <div className="mb-4 flex items-center rounded-lg bg-red-100 p-4 text-red-700 dark:bg-red-900 dark:text-red-200">
              <FaInfoCircle className="mr-2" />
              {financialDataError?.message ||
                t("Failed to load financial data")}
            </div>
          ) : (
            <>
              {/* Financial Data Table View */}
              <div className="mb-6 hidden overflow-x-auto sm:block">
                <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                  <thead className="bg-gray-50 dark:bg-gray-700">
                    <tr>
                      <th
                        className={`px-6 py-3 text-xs font-medium uppercase tracking-wider text-gray-500 dark:text-gray-300 ${
                          language === "ar" ? "text-right" : "text-left"
                        }`}
                      >
                        {t("Title")}
                      </th>
                      <th
                        className={`px-6 py-3 text-xs font-medium uppercase tracking-wider text-gray-500 dark:text-gray-300 ${
                          language === "ar" ? "text-right" : "text-left"
                        }`}
                      >
                        {t("Amount")}
                      </th>
                      <th
                        className={`px-6 py-3 text-xs font-medium uppercase tracking-wider text-gray-500 dark:text-gray-300 ${
                          language === "ar" ? "text-right" : "text-left"
                        }`}
                      >
                        {t("Date")}
                      </th>
                      <th
                        className={`px-6 py-3 text-xs font-medium uppercase tracking-wider text-gray-500 dark:text-gray-300 ${
                          language === "ar" ? "text-right" : "text-left"
                        }`}
                      >
                        {t("Type")}
                      </th>
                      <th
                        className={`px-6 py-3 text-xs font-medium uppercase tracking-wider text-gray-500 dark:text-gray-300 ${
                          language === "ar" ? "text-right" : "text-left"
                        }`}
                      >
                        {t("Status")}
                      </th>
                    </tr>
                  </thead>
                  <tbody className="divide-y divide-gray-200 bg-white dark:divide-gray-700 dark:bg-gray-800">
                    {(activeEventType === "income" ? incomeData : expenseData)
                      .length > 0 ? (
                      (activeEventType === "income"
                        ? incomeData
                        : expenseData
                      ).map((event) => {
                        // Ensure we have an ID for the key
                        const safeId = event.id || `event-${Math.random()}`;
                        return (
                          <tr
                            key={safeId}
                            className="hover:bg-gray-50 dark:hover:bg-gray-700"
                          >
                            <td className="whitespace-nowrap px-6 py-4 text-sm font-medium text-gray-900 dark:text-white">
                              {event.title || ""}
                            </td>
                            <td className="whitespace-nowrap px-6 py-4 text-sm text-gray-500 dark:text-gray-400">
                              {showActualAmount && activeEventType === "income"
                                ? formatCurrency(event.actual_amount)
                                : formatCurrency(event.amount)}
                            </td>
                            <td className="whitespace-nowrap px-6 py-4 text-sm text-gray-500 dark:text-gray-400">
                              {formatDate(event.dueDate)}
                            </td>
                            <td className="whitespace-nowrap px-6 py-4 text-sm text-gray-500 dark:text-gray-400">
                              {event.type || t("Payment")}
                            </td>
                            <td className="relative whitespace-nowrap px-6 py-4">
                              <button
                                className={`inline-flex rounded-full px-2 py-1 text-xs font-semibold leading-5 ${getStatusBadgeClass(
                                  event.status || "completed",
                                )}`}
                                onClick={(e) => {
                                  e.stopPropagation();
                                  setDropdownOpenId(
                                    safeId === dropdownOpenId ? null : safeId,
                                  );
                                }}
                                type="button"
                              >
                                {t(
                                  (event.status || "completed")
                                    .charAt(0)
                                    .toUpperCase() +
                                    (event.status || "completed").slice(1),
                                )}
                                <FaChevronDown className="ml-1 h-3 w-3" />
                              </button>
                              {dropdownOpenId === safeId && (
                                <div className="absolute left-0 z-10 mt-1 min-w-[120px] rounded border border-gray-200 bg-white shadow-lg dark:border-gray-700 dark:bg-gray-900">
                                  {statusOptions.map((status) => (
                                    <button
                                      key={status}
                                      className={`block w-full px-3 py-2 text-left text-xs hover:bg-gray-100 dark:hover:bg-gray-800 ${
                                        event.status === status
                                          ? "font-bold text-blue-600 dark:text-blue-400"
                                          : "text-gray-700 dark:text-gray-200"
                                      }`}
                                      onClick={(e) => {
                                        e.stopPropagation();
                                        handleEventStatusChange(
                                          safeId, // Use the safe ID here
                                          status,
                                          activeEventType,
                                        );
                                        setDropdownOpenId(null);
                                      }}
                                    >
                                      {t(status)}
                                    </button>
                                  ))}
                                </div>
                              )}
                            </td>
                          </tr>
                        );
                      })
                    ) : (
                      <tr>
                        <td
                          colSpan={5}
                          className="px-6 py-4 text-center text-sm text-gray-500 dark:text-gray-400"
                        >
                          {t(
                            `No ${activeEventType} data found for this reservation`,
                          )}
                        </td>
                      </tr>
                    )}
                  </tbody>
                </table>
              </div>

              {/* Mobile View for Financial Data */}
              <div className="mb-6 space-y-3 sm:hidden">
                {(activeEventType === "income" ? incomeData : expenseData)
                  .length > 0 ? (
                  (activeEventType === "income" ? incomeData : expenseData).map(
                    (event) => {
                      const safeId =
                        event.id || `event-mobile-${Math.random()}`;
                      return (
                        <div
                          key={safeId}
                          className="rounded-lg border border-gray-200 bg-white p-4 shadow-sm dark:border-gray-700 dark:bg-gray-800"
                        >
                          <div className="mb-2 flex items-start justify-between">
                            <span className="font-medium text-gray-900 dark:text-white">
                              {event.title}
                            </span>
                            <span
                              className={`rounded-full px-2 py-1 text-xs font-semibold ${getStatusBadgeClass(
                                event.status || "completed",
                              )}`}
                            >
                              {t(
                                (event.status || "completed")
                                  .charAt(0)
                                  .toUpperCase() +
                                  (event.status || "completed").slice(1),
                              )}
                            </span>
                          </div>
                          <div className="mt-2 space-y-2">
                            <div className="flex justify-between">
                              <span className="text-sm text-gray-500 dark:text-gray-400">
                                {t("Amount")}:
                              </span>
                              <span className="font-medium text-gray-900 dark:text-white">
                                {showActualAmount &&
                                activeEventType === "income"
                                  ? formatCurrency(event.actual_amount)
                                  : formatCurrency(event.amount)}
                              </span>
                            </div>
                            <div className="flex justify-between">
                              <span className="text-sm text-gray-500 dark:text-gray-400">
                                {t("Date")}:
                              </span>
                              <span className="text-gray-900 dark:text-white">
                                {formatDate(event.dueDate)}
                              </span>
                            </div>
                            <div className="flex justify-between">
                              <span className="text-sm text-gray-500 dark:text-gray-400">
                                {t("Type")}:
                              </span>
                              <span className="text-gray-900 dark:text-white">
                                {event.type || t("Payment")}
                              </span>
                            </div>
                          </div>
                        </div>
                      );
                    },
                  )
                ) : (
                  <div className="py-4 text-center text-gray-500 dark:text-gray-400">
                    {t(`No ${activeEventType} data found for this reservation`)}
                  </div>
                )}
              </div>

              {/* Progress Bar */}
              <div className="mt-4 rounded-lg bg-gray-50 p-3 dark:bg-gray-700 sm:mt-6 sm:p-4">
                <h4 className="text-md mb-2 font-semibold text-gray-800 dark:text-gray-200">
                  {activeEventType === "income"
                    ? t("Income Progress")
                    : t("Expense Progress")}
                </h4>

                {activeEventType === "income" ? (
                  <>
                    <div className="h-2.5 w-full rounded-full bg-gray-200 dark:bg-gray-600">
                      <div
                        className="h-2.5 rounded-full bg-blue-600"
                        style={{
                          width: `${
                            totalIncome ? (paidIncome / totalIncome) * 100 : 0
                          }%`,
                        }}
                      ></div>
                    </div>
                    <div className="mt-2 flex flex-wrap justify-between text-xs text-gray-500 dark:text-gray-400">
                      <span>
                        {t("Paid")}:{" "}
                        {totalIncome
                          ? ((paidIncome / totalIncome) * 100).toFixed(0)
                          : 0}
                        %
                      </span>
                      <span>
                        {t("Total")}: {formatCurrency(totalIncome)}
                      </span>
                    </div>
                  </>
                ) : (
                  <>
                    <div className="h-2.5 w-full rounded-full bg-gray-200 dark:bg-gray-600">
                      <div
                        className="h-2.5 rounded-full bg-red-600"
                        style={{
                          width: `${
                            totalExpense
                              ? (paidExpense / totalExpense) * 100
                              : 0
                          }%`,
                        }}
                      ></div>
                    </div>
                    <div className="mt-2 flex flex-wrap justify-between text-xs text-gray-500 dark:text-gray-400">
                      <span>
                        {t("Paid")}:{" "}
                        {totalExpense
                          ? ((paidExpense / totalExpense) * 100).toFixed(0)
                          : 0}
                        %
                      </span>
                      <span>
                        {t("Total")}: {formatCurrency(totalExpense)}
                      </span>
                    </div>
                  </>
                )}
              </div>
            </>
          )}
        </div>
      )}
    </div>
  );
};

export default ReservationFormInstallments;
