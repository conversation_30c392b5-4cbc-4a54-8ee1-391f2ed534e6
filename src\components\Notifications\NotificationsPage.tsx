"use client";

import React, { useState, useEffect } from 'react';
import Breadcrumb from '@/components/Breadcrumbs/Breadcrumb';
import { NotificationList } from '@/components/Notifications';
import { Notification, NotificationFilterOptions } from '@/lib/types/notification';
import NotificationServices from '@/lib/notifications';
import useLanguage from '@/hooks/useLanguage';
import { Bell, CheckCircle, Trash2, Filter } from 'lucide-react';

const NotificationsPage: React.FC = () => {
  const { t } = useLanguage();
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [filterOptions, setFilterOptions] = useState<NotificationFilterOptions>({
    type: 'all',
    read: null,
    timeframe: 'all',
    priority: 'all',
    search: '',
  });
  const [unreadCount, setUnreadCount] = useState(0);
  
  const notificationServices = NotificationServices();

  // Fetch notifications
  useEffect(() => {
    const abortController = new AbortController();
    
    const fetchNotifications = async () => {
      setIsLoading(true);
      try {
        const fetchedNotifications = await notificationServices.getNotifications(
          {}, // Empty filter to get all notifications
          abortController.signal
        );
        setNotifications(fetchedNotifications);
      } catch (error) {
        console.error('Failed to fetch notifications:', error);
      } finally {
        setIsLoading(false);
      }
    };
    
    fetchNotifications();
    
    return () => {
      abortController.abort();
    };
  }, []);

  // Handle marking notification as read
  const handleMarkAsRead = async (id: string) => {
    try {
      const success = await notificationServices.markAsRead(id);
      if (success) {
        setNotifications(notifications.map(notification => 
          notification.id === id 
            ? { ...notification, is_read: true } 
            : notification
        ));
      }
    } catch (error) {
      console.error('Failed to mark notification as read:', error);
    }
  };

  // Handle marking notification as unread
  const handleMarkAsUnread = async (id: string) => {
    try {
      const success = await notificationServices.markAsUnread(id);
      if (success) {
        setNotifications(notifications.map(notification => 
          notification.id === id 
            ? { ...notification, is_read: false } 
            : notification
        ));
        
        // Update unread count
        setUnreadCount(prev => prev + 1);
      }
    } catch (error) {
      console.error('Failed to mark notification as unread:', error);
    }
  };

  // Handle filter changes
  const handleFilterChange = (name: keyof NotificationFilterOptions, value: any) => {
    setFilterOptions(prevFilters => ({
      ...prevFilters,
      [name]: value
    }));
  };

  const handleMarkAllAsRead = async () => {
    try {
      // Get all unread notification IDs
      const unreadIds = notifications
        .filter(notification => !notification.is_read)
        .map(notification => notification.id);
      
      // Mark each as read sequentially
      for (const id of unreadIds) {
        await notificationServices.markAsRead(id);
      }
      
      // Update local state
      setNotifications(prevNotifications =>
        prevNotifications.map(notification => ({ ...notification, is_read: true }))
      );
    } catch (error) {
      console.error('Failed to mark all notifications as read:', error);
    }
  };

  // Reset filters
  const resetFilters = () => {
    setFilterOptions({
      type: 'all',
      read: null,
      timeframe: 'all',
      priority: 'all',
      search: '',
    });
  };

  return (
    <>
      <Breadcrumb pageName={t("notifications")} />

      <div className="grid grid-cols-1 gap-4 md:gap-6 mb-6">
        <div className="rounded-sm border border-stroke bg-white shadow-default dark:border-strokedark dark:bg-boxdark">
          <div className="border-b border-stroke py-4 px-6.5 dark:border-strokedark flex justify-between items-center flex-wrap gap-4">
            <div className="flex items-center">
              <Bell className="h-5 w-5 text-gray-600 dark:text-gray-300 mr-2" />
              <h3 className="font-medium text-black dark:text-white">
                {t('notifications')}
              </h3>
              {unreadCount > 0 && (
                <span className="ml-2 bg-meta-1 text-white text-xs font-bold px-2 py-0.5 rounded-full">
                  {unreadCount}
                </span>
              )}
            </div>
            
            <div className="flex flex-wrap gap-2">
              {/* Mark all as read button */}
              <button
                onClick={handleMarkAllAsRead}
                disabled={unreadCount === 0}
                className={`flex items-center text-xs font-medium py-1 px-2 rounded ${
                  unreadCount === 0
                    ? 'text-gray-400 dark:text-gray-600 cursor-not-allowed'
                    : 'text-blue-600 dark:text-blue-400 hover:bg-blue-50 dark:hover:bg-blue-900/20'
                }`}
              >
                <CheckCircle className="h-3.5 w-3.5 mr-1.5" />
                {t('markAllRead')}
              </button>
              
              {/* Search */}
              <div className="relative">
                <input
                  type="text"
                  placeholder={t('searchNotifications')}
                  className="w-full rounded-lg border border-stroke bg-transparent py-1 pl-3 pr-10 text-xs outline-none focus:border-primary dark:border-strokedark dark:bg-meta-4 dark:focus:border-primary"
                  value={filterOptions.search || ''}
                  onChange={(e) => handleFilterChange('search', e.target.value)}
                />
                <span className="absolute right-3 top-1/2 -translate-y-1/2">
                  <svg
                    className="fill-body dark:fill-bodydark"
                    width="12"
                    height="12"
                    viewBox="0 0 16 16"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M10.4 8.8C11.1333 7.86667 11.5 6.7 11.5 5.3C11.5 3.9 11.1333 2.73333 10.4 1.8C9.73333 0.866666 8.8 0.4 7.6 0.4C6.4 0.4 5.46667 0.866666 4.8 1.8C4.13333 2.73333 3.80001 3.9 3.80001 5.3C3.80001 6.7 4.16667 7.86667 4.90001 8.8C5.63334 9.73333 6.63334 10.2 7.90001 10.2C9.16667 10.2 10.1333 9.73333 10.7 8.8H10.4ZM12.5 11.3L15.4 14.2L14.2 15.4L11.3 12.5C10.6 13.0667 9.8 13.5 8.90001 13.8C8.00001 14.1 7.06667 14.2333 6.10001 14.2C4.30001 14.2 2.76667 13.6167 1.5 12.45C0.5 11.45 0 10.1333 0 8.5C0 6.9 0.433333 5.55 1.3 4.45C2.16667 3.28333 3.30001 2.46667 4.7 2H4.9C5.9 2 6.83334 2.2 7.7 2.6C8.56667 3 9.30001 3.53333 9.90001 4.2C10.5 4.86667 10.9667 5.66667 11.3 6.6C11.6333 7.46667 11.8 8.36667 11.8 9.3C11.8 10 11.7 10.6333 11.5 11.2C11.3 11.7667 11 12.2667 10.6 12.7L12.5 11.3Z"
                      fill=""
                    />
                  </svg>
                </span>
              </div>
              
              {/* Read Status Filter */}
              <select
                className="rounded-lg border border-stroke bg-transparent px-3 py-1 text-xs outline-none focus:border-primary dark:border-strokedark dark:bg-meta-4 dark:focus:border-primary"
                value={filterOptions.read === true ? 'read' : filterOptions.read === false ? 'unread' : 'all'}
                onChange={(e) => {
                  const value = e.target.value;
                  handleFilterChange('read', value === 'read' ? true : value === 'unread' ? false : null);
                }}
              >
                <option value="all">{t('allNotifications')}</option>
                <option value="read">{t('readNotifications')}</option>
                <option value="unread">{t('unreadNotifications')}</option>
              </select>

              {/* Priority Filter */}
              <select
                className="rounded-lg border border-stroke bg-transparent px-3 py-1 text-xs outline-none focus:border-primary dark:border-strokedark dark:bg-meta-4 dark:focus:border-primary"
                value={filterOptions.priority || 'all'}
                onChange={(e) => handleFilterChange('priority', e.target.value)}
              >
                <option value="all">{t('allPriorities')}</option>
                <option value="high">{t('highPriority')}</option>
                <option value="medium">{t('mediumPriority')}</option>
                <option value="low">{t('lowPriority')}</option>
              </select>

              {/* Time Frame Filter */}
              <select
                className="rounded-lg border border-stroke bg-transparent px-3 py-1 text-xs outline-none focus:border-primary dark:border-strokedark dark:bg-meta-4 dark:focus:border-primary"
                value={filterOptions.timeframe || 'all'}
                onChange={(e) => handleFilterChange('timeframe', e.target.value as 'all' | 'today' | 'week' | 'month')}
              >
                <option value="all">{t('allTime')}</option>
                <option value="today">{t('today')}</option>
                <option value="week">{t('thisWeek')}</option>
                <option value="month">{t('thisMonth')}</option>
              </select>

              {/* Reset filters button */}
              <button
                onClick={resetFilters}
                className="flex items-center justify-center gap-1 rounded bg-primary py-1 px-2 text-xs font-medium text-white hover:bg-opacity-80"
              >
                <Filter className="h-3 w-3" />
                {t('resetFilters')}
              </button>
            </div>
          </div>

          <div className="p-4 md:p-6.5">
            <NotificationList
              notifications={notifications}
              filterOptions={filterOptions}
              onMarkAsRead={handleMarkAsRead}
              onMarkAsUnread={handleMarkAsUnread}
              isLoading={isLoading}
            />
          </div>
        </div>
      </div>
    </>
  );
};

export default NotificationsPage;
