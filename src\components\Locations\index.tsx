"use client";

import React, { useState } from "react";
import { MapPin, Plus, Building } from "lucide-react";
import { Card, CardContent } from "@/components/cards/card";
import LocationForm from "@/components/Locations/LocationForm";
import LocationList from "@/components/Locations/LocationList";
import LocationDetails from "@/components/Locations/LocationDetails";
import InlineEditForm from "@/components/Locations/InlineEditForm";
import DefaultLayout from "@/components/Layouts/Layout";
import Breadcrumb from "@/components/Breadcrumbs/Breadcrumb";
import { Location } from "@/lib/types/location";
import useLanguage from "@/hooks/useLanguage";

const LocationPage = () => {
  const [selectedLocation, setSelectedLocation] = useState<Location | null>(
    null,
  );
  const [isFormOpen, setIsFormOpen] = useState(false);
  const [isEditing, setIsEditing] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [editingLocation, setEditingLocation] = useState<Location | null>(null);
  const { t } = useLanguage();

  const handleSaveLocation = (data: Partial<Location>) => {
    console.log("Form submitted with data:", data);

    if (isEditing && selectedLocation) {
      setSelectedLocation({
        ...selectedLocation,
        ...data,
      } as Location);
    }

    setIsFormOpen(false);
    setIsEditing(false);
    setIsEditModalOpen(false);
    setEditingLocation(null);
  };

  const handleEditFromList = (location: Location) => {
    setEditingLocation(location);
    setIsEditModalOpen(true);
  };

  const handleCloseEditModal = () => {
    setIsEditModalOpen(false);
    setEditingLocation(null);
  };

  return (
    <>
      <Breadcrumb pageName={t("locations")} />
      <div className="min-h-screen bg-gray-50 p-4 dark:bg-boxdark">
        {/* Title Row */}
        <div className="mb-4">
          <div className="flex items-center gap-3">
            <Building size={24} className="text-blue-600" />
            <h1 className="text-2xl font-bold text-gray-900 dark:text-gray-100">
              {t("locationsManagement")}
            </h1>
          </div>
        </div>

        {/* Add Location Button Section */}
        {!isFormOpen && (
          <div className="mb-6">
            <Card className="w-full border-blue-100 bg-gradient-to-r from-blue-50 to-indigo-50 dark:border-blue-800/50 dark:from-blue-900/20 dark:to-indigo-900/20">
              <CardContent className="py-6">
                <div className="flex flex-col items-center justify-between md:flex-row">
                  <div className="mb-4 md:mb-0">
                    <h2 className="text-xl font-bold text-blue-800 dark:text-blue-300">
                      {t("addNewLocation")}
                    </h2>
                    <p className="mt-1 text-blue-600 dark:text-blue-400">
                      {t("createNewLocationDescription")}
                    </p>
                  </div>
                  <button
                    onClick={() => {
                      setIsFormOpen(true);
                      setIsEditing(false);
                    }}
                    className="flex w-full items-center justify-center gap-3 rounded-lg bg-blue-600 px-8 py-3 text-lg font-medium text-white shadow-md transition-colors hover:bg-blue-700 hover:shadow-lg md:w-auto"
                  >
                    <Plus size={22} />
                    {t("addLocation")}
                  </button>
                </div>
              </CardContent>
            </Card>
          </div>
        )}

        {isFormOpen && !isEditing && (
          <div className="mb-6">
            <Card className="w-full border-blue-100 bg-gradient-to-r from-blue-50 to-indigo-50 shadow-md dark:border-blue-800/50 dark:from-blue-900/20 dark:to-indigo-900/20">
              <CardContent className="p-6">
                <div className="mb-4">
                  <h2 className="text-center text-2xl font-bold text-blue-800 dark:text-blue-300">
                    {t("addNewLocation")}
                  </h2>
                  <p className="mt-1 text-center text-blue-600 dark:text-blue-400">
                    {t("fillLocationDetails")}
                  </p>
                </div>
                <div className="mx-auto max-w-4xl">
                  <LocationForm
                    isOpen={isFormOpen}
                    onClose={() => setIsFormOpen(false)}
                    onSubmit={handleSaveLocation}
                    view="sidebar"
                  />
                </div>
              </CardContent>
            </Card>
          </div>
        )}

        {/* Add Edit Modal */}
        {isEditModalOpen && editingLocation && (
          <div className="mb-6">
            <Card className="w-full border-blue-100 bg-gradient-to-r from-blue-50 to-indigo-50 shadow-md dark:border-blue-800/50 dark:from-blue-900/20 dark:to-indigo-900/20">
              <CardContent className="p-6">
                <div className="mb-4">
                  <h2 className="text-center text-2xl font-bold text-blue-800 dark:text-blue-300">
                    {t("editLocation")}
                  </h2>
                  <p className="mt-1 text-center text-blue-600 dark:text-blue-400">
                    {t("updateLocationDetails")}
                  </p>
                </div>
                <div className="mx-auto max-w-4xl">
                  <LocationForm
                    isOpen={isEditModalOpen}
                    onClose={handleCloseEditModal}
                    location={editingLocation}
                    onSubmit={handleSaveLocation}
                    view="sidebar"
                  />
                </div>
              </CardContent>
            </Card>
          </div>
        )}

        {/* Main Content Area */}
        <div className="mb-6">
          {selectedLocation ? (
            isEditing ? (
              <InlineEditForm
                location={selectedLocation}
                onSave={handleSaveLocation}
                onCancel={() => setIsEditing(false)}
              />
            ) : (
              <LocationDetails
                location={selectedLocation}
                onEdit={() => setIsEditing(true)}
                onDelete={() => {
                  console.log(
                    `Deleting location with ID: ${selectedLocation?.id}`,
                  );
                  setSelectedLocation(null);
                }}
                onClose={() => setSelectedLocation(null)}
              />
            )
          ) : (
            <Card className="flex w-full items-center justify-center dark:border-gray-700 dark:bg-gray-800">
              <CardContent className="py-12 text-center">
                <MapPin size={48} className="mx-auto mb-4 text-gray-400" />
                <p className="text-gray-500 dark:text-gray-400">
                  {t("selectALocationToViewDetails")}
                </p>
              </CardContent>
            </Card>
          )}
        </div>
        {!selectedLocation && (
          <div>
            <LocationList
              onSelect={(location: Location) => setSelectedLocation(location)}
              onEdit={handleEditFromList}
            />
          </div>
        )}
      </div>
    </>
  );
};

export default LocationPage;
