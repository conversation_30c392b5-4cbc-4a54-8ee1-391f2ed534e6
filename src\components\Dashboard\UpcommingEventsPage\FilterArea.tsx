import React, { useState } from "react";
import { FiSearch } from "react-icons/fi";
import { FaTimes, FaFilter } from "react-icons/fa";
import Select from 'react-select';
import useLanguage from "@/hooks/useLanguage";
import DatePicker from "react-datepicker";
import "react-datepicker/dist/react-datepicker.css";
import { enUS, arSA } from "date-fns/locale";

interface FilterAreaProps {
  filterType: "All" | "income" | "expense";
  setFilterType: (value: "All" | "income" | "expense") => void;
  filterStatusOptions: string[];
  filterStatus: string;
  setFilterStatus: (value: string | null) => void;
  searchQuery: string;
  handleSearch: (e: React.ChangeEvent<HTMLInputElement>) => void;
  startDate: Date | null;
  setStartDate: (value: Date | null) => void;
  endDate: Date | null;
  setEndDate: (value: Date | null) => void;
  clearFilters: () => void;
  filterContact: string;
  setFilterContact: (value: string) => void;
  filterLocation: string;
  setFilterLocation: (value: string) => void;
  filterPriceFrom: string;
  setFilterPriceFrom: (value: string) => void;
  filterPriceTo: string;
  setFilterPriceTo: (value: string) => void;
  contactOptions: { value: string; label: string }[];
  locationOptions: { value: string; label: string }[];
  createdAtStart: Date | null;
  setCreatedAtStart: (value: Date | null) => void;
  createdAtEnd: Date | null;
  setCreatedAtEnd: (value: Date | null) => void;
}

const FilterArea: React.FC<FilterAreaProps> = ({
  filterType,
  setFilterType,
  filterStatus,
  filterStatusOptions,
  setFilterStatus,
  searchQuery,
  handleSearch,
  startDate,
  setStartDate,
  endDate,
  setEndDate,
  clearFilters,
  filterContact,
  setFilterContact,
  filterLocation,
  setFilterLocation,
  filterPriceFrom,
  setFilterPriceFrom,
  filterPriceTo,
  setFilterPriceTo,
  contactOptions,
  locationOptions,
  createdAtStart,
  setCreatedAtStart,
  createdAtEnd,
  setCreatedAtEnd,
}) => {
  const { t, language } = useLanguage();
  const [isFilterOpen, setIsFilterOpen] = useState(false);

  const locale = language === "ar" ? arSA : enUS;

  return (
    <div className="mb-6">
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-4 mt-4 grid gap-4 sm:grid-cols-2 lg:grid-cols-4">
        {/* Search */}
        <div className="sm:col-span-2 lg:col-span-4 relative">
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
            {t("search")}
          </label>
          <div className="relative mt-1 flex">
            <FiSearch className="absolute top-1/2 transform -translate-y-1/2 left-3 text-gray-500 dark:text-gray-400" />
            <input
              type="text"
              value={searchQuery}
              onChange={handleSearch}
              placeholder={t("search events, contacts, or locations")}
              className="pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm w-full bg-white dark:bg-gray-700 dark:text-gray-200"
            />
            <button onClick={() => handleSearch({ target: { value: "" } } as React.ChangeEvent<HTMLInputElement>)} className="ml-2 mt-1 p-2 text-gray-500 dark:text-gray-300">
              <FaTimes size={12} />
            </button>
          </div>
        </div>

        {/* Filter by Type */}
        <div className="sm:col-span-1">
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
            {t("type")}
          </label>
          <div className="relative mt-1 flex">
            <select
              value={filterType}
              onChange={(e) => setFilterType(e.target.value as "All" | "income" | "expense")}
              className="block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm bg-white dark:bg-gray-700 dark:text-gray-200"
            >
              <option value="All">{t('all')}</option>
              <option value="income">{t('income')}</option>
              <option value="expense">{t('expense')}</option>
            </select>
            <button onClick={() => setFilterType("All")} className="ml-2 mt-1 p-2 text-gray-500 dark:text-gray-300">
              <FaTimes size={12} />
            </button>
          </div>
        </div>

        {/* Filter by Status */}
        <div className="sm:col-span-1">
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
            {t("status")}
          </label>
          <div className="relative mt-1 flex">
            <select
              value={filterStatus}
              onChange={(e) => setFilterStatus(e.target.value as "All" | "pending" | "completed" | "cancelled")}
              className="block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm bg-white dark:bg-gray-700 dark:text-gray-200"
            >
              {filterStatusOptions.map((status) => (
                <option key={status} value={status}>
                  {t(status)}
                </option>
              ))}

            </select>
            <button onClick={() => setFilterStatus("All")} className="ml-2 mt-1 p-2 text-gray-500 dark:text-gray-300">
              <FaTimes size={12} />
            </button>
          </div>
        </div>

        {/* Date Range Filter */}
        <div className="sm:col-span-1">
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
            {t("start date")}
          </label>
          <div className="relative mt-1 flex">
            <DatePicker
              selected={startDate}
              onChange={(date: Date | null) => setStartDate(date)}
              className="block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm bg-white dark:bg-gray-700 dark:text-gray-200"
              dateFormat="yyyy-MM-dd"
              locale={locale}
              placeholderText={t("select date")}
            />
            <button onClick={() => setStartDate(null)} className="ml-2 mt-1 p-2 text-gray-500 dark:text-gray-300">
              <FaTimes size={12} />
            </button>
          </div>
        </div>
        <div className="sm:col-span-1">
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
            {t("end date")}
          </label>
          <div className="relative mt-1 flex">
            <DatePicker
              selected={endDate}
              onChange={(date: Date | null) => setEndDate(date)}
              className="block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm bg-white dark:bg-gray-700 dark:text-gray-200"
              dateFormat="yyyy-MM-dd"
              locale={locale}
              placeholderText={t("select date")}
            />
            <button onClick={() => setEndDate(null)} className="ml-2 mt-1 p-2 text-gray-500 dark:text-gray-300">
              <FaTimes size={12} />
            </button>
          </div>
        </div>

        {/* Filter by Contact */}
        <div className="sm:col-span-1">
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
            {t("contact")}
          </label>
          <div className="relative mt-1 flex">
            <Select
              placeholder={t("select...")}
              options={[{ value: "all", label: t("All") }, ...Array.from(new Map(contactOptions.map(option => [option.value, option])).values())]}
              value={contactOptions.find(option => option.value === filterContact)}
              onChange={(selectedOption) => setFilterContact(selectedOption ? selectedOption.value : "all")}
              className="mt-1 flex-grow"
              isSearchable={true}
            />
            <button onClick={() => setFilterContact("all")} className="ml-2 mt-1 p-2 text-gray-500 dark:text-gray-300">
              <FaTimes size={12} />
            </button>
          </div>
        </div>

        {/* Filter by Location */}
        <div className="sm:col-span-1">
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
            {t("location")}
          </label>
          <div className="relative mt-1 flex">
            <Select
              placeholder={t("select...")}
              options={[{ value: "all", label: t("All") }, ...Array.from(new Map(locationOptions.map(option => [option.value, option])).values())]}
              value={locationOptions.find(option => option.value === filterLocation)}
              onChange={(selectedOption) => setFilterLocation(selectedOption ? selectedOption.value : "all")}
              className="mt-1 flex-grow"
              isSearchable={true}
            />
            <button onClick={() => setFilterLocation("all")} className="ml-2 mt-1 p-2 text-gray-500 dark:text-gray-300">
              <FaTimes size={12} />
            </button>
          </div>
        </div>

        {/* Filter by Price From */}
        <div className="sm:col-span-1">
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
            {t("price from")}
          </label>
          <div className="relative mt-1 flex">
            <input
              type="number"
              value={filterPriceFrom}
              onChange={(e) => setFilterPriceFrom(e.target.value)}
              className="block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm bg-white dark:bg-gray-700 dark:text-gray-200"
              placeholder={t("min price")}
            />
            <button onClick={() => setFilterPriceFrom("")} className="ml-2 mt-1 p-2 text-gray-500 dark:text-gray-300">
              <FaTimes size={12} />
            </button>
          </div>
        </div>

        {/* Filter by Price To */}
        <div className="sm:col-span-1">
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
            {t("price to")}
          </label>
          <div className="relative mt-1 flex">
            <input
              type="number"
              value={filterPriceTo}
              onChange={(e) => setFilterPriceTo(e.target.value)}
              className="block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm bg-white dark:bg-gray-700 dark:text-gray-200"
              placeholder={t("max price")}
            />
            <button onClick={() => setFilterPriceTo("")} className="ml-2 mt-1 p-2 text-gray-500 dark:text-gray-300">
              <FaTimes size={12} />
            </button>
          </div>
        </div>

        {/* Filter by Created At Start Date */}
        <div className="sm:col-span-1">
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
            {t("created at start date")}
          </label>
          <div className="relative mt-1 flex">
            <DatePicker
              selected={createdAtStart}
              onChange={(date: Date | null) => setCreatedAtStart(date)}
              className="block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm bg-white dark:bg-gray-700 dark:text-gray-200"
              dateFormat="yyyy-MM-dd"
              locale={locale}
              placeholderText={t("select date")}
            />
            <button onClick={() => setCreatedAtStart(null)} className="ml-2 mt-1 p-2 text-gray-500 dark:text-gray-300">
              <FaTimes size={12} />
            </button>
          </div>
        </div>

        {/* Filter by Created At End Date */}
        <div className="sm:col-span-1">
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
            {t("created at end date")}
          </label>
          <div className="relative mt-1 flex">
            <DatePicker
              selected={createdAtEnd}
              onChange={(date: Date | null) => setCreatedAtEnd(date)}
              className="block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm bg-white dark:bg-gray-700 dark:text-gray-200"
              dateFormat="yyyy-MM-dd"
              locale={locale}
              placeholderText={t("select date")}
            />
            <button onClick={() => setCreatedAtEnd(null)} className="ml-2 mt-1 p-2 text-gray-500 dark:text-gray-300">
              <FaTimes size={12} />
            </button>
          </div>
        </div>

        {/* Clear All Filters */}
        <div className="sm:col-span-2 lg:col-span-4 flex justify-end">
          <button onClick={clearFilters} className="p-2 bg-red-500 text-white rounded">{t("clear all filters")}</button>
        </div>
      </div>
    </div>
  );
};

export default FilterArea;