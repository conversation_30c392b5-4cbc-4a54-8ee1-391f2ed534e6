import React, { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Loader2,
  TrendingUp,
  TrendingDown,
  DollarSign,
  FileText,
  Calendar,
  Building,
} from "lucide-react";

// Import the tab components
import ContractsTabComponent from "./Tabs/ContractsTab";
import ExpenseEventsTabComponent from "./Tabs/ExpenseEventsTab";
import ReservationsTabComponent from "./Tabs/ReservationsTab";
import IncomeEventsTabComponent from "./Tabs/IncomeEventsTab";
import useLanguage from "@/hooks/useLanguage";

export interface FinancialHistoryProps {
  locationId: string;
  isLoading?: boolean;
}

export const FinancialHistory: React.FC<FinancialHistoryProps> = ({
  locationId,
  isLoading = false,
}) => {
  // State for active tabs
  const { t } = useLanguage();
  const [activeMainTab, setActiveMainTab] = useState<string>("income");
  const [activeIncomeTab, setActiveIncomeTab] =
    useState<string>("reservations");
  const [activeExpenseTab, setActiveExpenseTab] = useState<string>("contracts");

  // Display loading state if data is being fetched
  if (isLoading) {
    return (
      <div className="w-full max-w-full overflow-hidden">
        <Card className="w-full max-w-full overflow-hidden border-0 bg-gradient-to-br from-blue-50 to-indigo-100 shadow-lg dark:from-gray-800 dark:to-gray-900">
          <CardHeader className="px-6 py-6 sm:px-8">
            <div className="flex items-center gap-3">
              <div className="rounded-lg bg-blue-500 p-2">
                <DollarSign className="h-6 w-6 text-white" />
              </div>
              <div>
                <CardTitle className="bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-xl font-bold text-transparent sm:text-2xl">
                  {t("financialHistory")}
                </CardTitle>
                <CardDescription className="text-sm text-gray-600 dark:text-gray-400">
                  {t("Loading financial data...")}
                </CardDescription>
              </div>
            </div>
          </CardHeader>
          <CardContent className="flex items-center justify-center p-8 sm:p-12">
            <div className="flex flex-col items-center gap-4">
              <Loader2 className="h-8 w-8 animate-spin text-blue-500" />
              <p className="text-sm text-gray-500 dark:text-gray-400">
                {t("Fetching your financial records...")}
              </p>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="w-full max-w-full overflow-hidden">
      <Card className="w-full max-w-full overflow-hidden border-0 bg-gradient-to-br from-white via-gray-50 to-blue-50 shadow-xl dark:from-gray-900 dark:via-gray-800 dark:to-gray-900">
        <CardHeader className="bg-gradient-to-r from-blue-500 to-indigo-600 px-6 py-6 text-white sm:px-8">
          <div className="flex items-center gap-4">
            <div className="rounded-xl bg-white/20 p-3 backdrop-blur-sm">
              <DollarSign className="h-8 w-8 text-white" />
            </div>
            <div>
              <CardTitle className="text-2xl font-bold text-white sm:text-3xl">
                {t("Financial History")}
              </CardTitle>
              <CardDescription className="text-base text-blue-100">
                {t(
                  "Complete overview of all financial records for this location",
                )}
              </CardDescription>
            </div>
          </div>
        </CardHeader>
        <CardContent className="w-full max-w-full overflow-hidden px-6 py-6 sm:px-8">
          {/* Main tabs: Income and Expenses */}
          <div className="w-full max-w-full overflow-hidden">
            <Tabs
              defaultValue="income"
              value={activeMainTab}
              onValueChange={setActiveMainTab}
              className="w-full max-w-full overflow-hidden"
            >
              <TabsList className="mb-6 grid h-auto w-full max-w-full grid-cols-2 rounded-xl bg-gray-100 p-2 shadow-inner dark:bg-gray-800">
                <TabsTrigger
                  value="income"
                  className="min-w-0 truncate rounded-lg px-6 py-4 text-sm font-semibold transition-all duration-200 hover:bg-white/50 data-[state=active]:bg-gradient-to-r data-[state=active]:from-green-500 data-[state=active]:to-emerald-600 data-[state=active]:text-white data-[state=active]:shadow-lg dark:hover:bg-gray-700 sm:text-base"
                >
                  <div className="flex items-center gap-2">
                    <TrendingUp className="h-4 w-4" />
                    <span>{t("Income")}</span>
                  </div>
                </TabsTrigger>
                <TabsTrigger
                  value="expenses"
                  className="min-w-0 truncate rounded-lg px-6 py-4 text-sm font-semibold transition-all duration-200 hover:bg-white/50 data-[state=active]:bg-gradient-to-r data-[state=active]:from-red-500 data-[state=active]:to-orange-600 data-[state=active]:text-white data-[state=active]:shadow-lg dark:hover:bg-gray-700 sm:text-base"
                >
                  <div className="flex items-center gap-2">
                    <TrendingDown className="h-4 w-4" />
                    <span>{t("Expenses")}</span>
                  </div>
                </TabsTrigger>
              </TabsList>

              {/* Income Tab Content */}
              <TabsContent
                value="income"
                className="mt-6 w-full max-w-full overflow-hidden"
              >
                <div className="w-full max-w-full overflow-hidden rounded-xl bg-gradient-to-br from-green-50 to-emerald-50 p-4 dark:from-green-900/20 dark:to-emerald-900/20">
                  <Tabs
                    defaultValue="reservations"
                    value={activeIncomeTab}
                    onValueChange={setActiveIncomeTab}
                    className="w-full max-w-full overflow-hidden"
                  >
                    <TabsList className="mb-4 grid h-auto w-full max-w-full grid-cols-1 gap-2 rounded-lg bg-white/70 p-2 shadow-sm dark:bg-gray-800/70 sm:grid-cols-2 sm:gap-1">
                      <TabsTrigger
                        value="reservations"
                        className="mb-1 min-w-0 truncate rounded-md px-4 py-3 text-xs font-medium transition-all duration-200 hover:bg-green-100 data-[state=active]:bg-green-500 data-[state=active]:text-white data-[state=active]:shadow-md dark:hover:bg-green-800 sm:mb-0 sm:text-sm"
                      >
                        <div className="flex items-center gap-2">
                          <Calendar className="h-4 w-4" />
                          <span>{t("Reservations")}</span>
                        </div>
                      </TabsTrigger>
                      <TabsTrigger
                        value="events"
                        className="min-w-0 truncate rounded-md px-4 py-3 text-xs font-medium transition-all duration-200 hover:bg-green-100 data-[state=active]:bg-green-500 data-[state=active]:text-white data-[state=active]:shadow-md dark:hover:bg-green-800 sm:text-sm"
                      >
                        <div className="flex items-center gap-2">
                          <FileText className="h-4 w-4" />
                          <span>{t("Income Events")}</span>
                        </div>
                      </TabsTrigger>
                    </TabsList>

                    <TabsContent
                      value="reservations"
                      className="mt-4 w-full max-w-full overflow-hidden"
                    >
                      <div className="w-full max-w-full overflow-hidden rounded-lg bg-white shadow-lg dark:bg-gray-800">
                        <div className="w-full min-w-0 max-w-full overflow-hidden">
                          <ReservationsTabComponent locationId={locationId} />
                        </div>
                      </div>
                    </TabsContent>

                    <TabsContent
                      value="events"
                      className="mt-4 w-full max-w-full overflow-hidden"
                    >
                      <div className="w-full max-w-full overflow-hidden rounded-lg bg-white shadow-lg dark:bg-gray-800">
                        <div className="w-full min-w-0 max-w-full overflow-hidden">
                          <IncomeEventsTabComponent locationId={locationId} />
                        </div>
                      </div>
                    </TabsContent>
                  </Tabs>
                </div>
              </TabsContent>

              {/* Expenses Tab Content */}
              <TabsContent
                value="expenses"
                className="mt-6 w-full max-w-full overflow-hidden"
              >
                <div className="w-full max-w-full overflow-hidden rounded-xl bg-gradient-to-br from-red-50 to-orange-50 p-4 dark:from-red-900/20 dark:to-orange-900/20">
                  <Tabs
                    defaultValue="contracts"
                    value={activeExpenseTab}
                    onValueChange={setActiveExpenseTab}
                    className="w-full max-w-full overflow-hidden"
                  >
                    <TabsList className="mb-4 grid h-auto w-full max-w-full grid-cols-1 gap-2 rounded-lg bg-white/70 p-2 shadow-sm dark:bg-gray-800/70 sm:grid-cols-2 sm:gap-1">
                      <TabsTrigger
                        value="contracts"
                        className="mb-1 min-w-0 truncate rounded-md px-4 py-3 text-xs font-medium transition-all duration-200 hover:bg-red-100 data-[state=active]:bg-red-500 data-[state=active]:text-white data-[state=active]:shadow-md dark:hover:bg-red-800 sm:mb-0 sm:text-sm"
                      >
                        <div className="flex items-center gap-2">
                          <Building className="h-4 w-4" />
                          <span>{t("Contracts")}</span>
                        </div>
                      </TabsTrigger>
                      <TabsTrigger
                        value="events"
                        className="min-w-0 truncate rounded-md px-4 py-3 text-xs font-medium transition-all duration-200 hover:bg-red-100 data-[state=active]:bg-red-500 data-[state=active]:text-white data-[state=active]:shadow-md dark:hover:bg-red-800 sm:text-sm"
                      >
                        <div className="flex items-center gap-2">
                          <FileText className="h-4 w-4" />
                          <span>{t("expenseEvents")}</span>
                        </div>
                      </TabsTrigger>
                    </TabsList>

                    <TabsContent
                      value="contracts"
                      className="mt-4 w-full max-w-full overflow-hidden"
                    >
                      <div className="w-full max-w-full overflow-hidden rounded-lg bg-white shadow-lg dark:bg-gray-800">
                        <div className="w-full min-w-0 max-w-full overflow-hidden">
                          <ContractsTabComponent locationId={locationId} />
                        </div>
                      </div>
                    </TabsContent>

                    <TabsContent
                      value="events"
                      className="mt-4 w-full max-w-full overflow-hidden"
                    >
                      <div className="w-full max-w-full overflow-hidden rounded-lg bg-white shadow-lg dark:bg-gray-800">
                        <div className="w-full min-w-0 max-w-full overflow-hidden">
                          <ExpenseEventsTabComponent locationId={locationId} />
                        </div>
                      </div>
                    </TabsContent>
                  </Tabs>
                </div>
              </TabsContent>
            </Tabs>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default FinancialHistory;
