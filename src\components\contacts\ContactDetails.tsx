import React, { useEffect, useState } from "react";
import {
  Mail,
  Phone,
  MapPin,
  Edit2,
  Trash2,
  X,
  ArrowUpRight,
  ArrowDownRight,
  Minus,
  History,
  PersonStanding,
} from "lucide-react";
import {
  <PERSON>,
  CardContent,
  CardHeader,
  CardTitle,
} from "@/components/cards/card";
import { Contact, contactLoaction } from "@/lib/types/contacts";
import { Button } from "@/components/ui/button";
import useLanguage from "@/hooks/useLanguage";
import { useRouter } from "next/navigation";
import { toast } from "react-hot-toast";
import { useContactServices } from "@/hooks/useContact";
import { EventDetails } from "@/lib/interfaces/finaces";
import UpcomingEventsPage from "../Dashboard/UpcommingEventsPage/UpcommingEvents";
import { generatePDF } from "../Dashboard/UpcommingEventsPage/generatePDF";
import DeleteConfirmationModal from "../Modals/DeleteConfirmationModal";
import { Reservation } from "@/lib/interfaces/reservation";
import ReservationList from "../Reservations/ReservationList";
import { SuccessPopup } from "../common/successPopUp";
import { ErrorPopup } from "../common/errorPopUp";

interface ContactDetailsProps {
  contact: Contact;
  onEdit: () => void;
  onDelete: () => void;
  onClose: () => void;
}

const LocationModal = ({
  locations,
  title,
  onClose,
}: {
  locations: string[];
  title: string;
  onClose: () => void;
}) => (
  <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/50">
    <div className="w-full max-w-md rounded-lg bg-white p-6 dark:bg-boxdark">
      <div className="mb-4 flex items-center justify-between">
        <h2 className="text-xl font-bold dark:text-gray-100">{title}</h2>
        <button
          onClick={onClose}
          className="text-gray-600 hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-100"
        >
          <X size={24} />
        </button>
      </div>
      <div className="space-y-2">
        {locations.map((location, index) => (
          <div
            key={index}
            className="rounded bg-gray-100 p-3 dark:bg-gray-700 dark:text-gray-100"
          >
            {location}
          </div>
        ))}
      </div>
    </div>
  </div>
);

export const ContactDetails = ({
  contact,
  onEdit,
  onDelete,
  onClose,
}: ContactDetailsProps) => {
  const { t, language } = useLanguage();
  const router = useRouter();
  const contactService = useContactServices();
  const [locationModalType, setLocationModalType] = useState<
    "shared" | "owned" | null
  >(null);
  const [activeTab, setActiveTab] = useState<"incomes" | "expenses" | "all">(
    "incomes",
  );
  const [contactEvents, setContactEvents] = useState<EventDetails[]>([]);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);
  const [deleteError, setDeleteError] = useState<string | null>(null);
  const [showReservations, setShowReservations] = useState(false);
  const [showFinances, setShowFinances] = useState(false);
  const [isSuccessPopup, setSuccessPopup] = useState(false);
  const [isErrorPopup, setErrorPopup] = useState(false);
  const [message, setMessage] = useState("");
  const [contactReservations , setContactReservations] = useState<Reservation[]>([]);
  useEffect(() => {
    if (contact) {
      console.log("Fetching contact finances for:", contact.id);

      contactService
        .getContactFinances(contact.id)
        .then((data) => {
          setContactEvents(data.incomes);
          setContactEvents((prev) => [...prev, ...data.expenses]);
        })
        .catch((error) => {
          console.error("Error fetching contact finances:", error);
          toast.error(t("errorFetchingContactFinances"));
        });
    }
    console.log("Contact events fetched:", contactEvents);
  }, [contact]);

  useEffect(() => {
    if (!contact) return;
    contactService
      .getContactReservations(contact.id)
      .then((data) => {
        setContactReservations(data);
      })
      .catch(() => {
        toast.error(t("errorFetchingContactReservations"));
      });
  }, [contact]);

  useEffect(() => {
    if (!contact) return;
    contactService
      .getContactFinances(contact.id)
      .then((data) => {
        setContactEvents([...data.incomes, ...data.expenses]);
      })
      .catch(() => {
        toast.error(t("errorFetchingContactFinances"));
      });
  }, [contact]);

  if (!contact) {
    return (
      <Card className="h-full w-full">
        <CardContent className="flex flex-col items-center justify-center py-16">
          <PersonStanding className="h-16 w-16 text-gray-300 dark:text-gray-600" />
          <p className="mt-4 text-center text-gray-500 dark:text-gray-400">
            {t("noContactSelected")}
          </p>
        </CardContent>
      </Card>
    );
  }

  const getBalanceIndicator = () => {
    const balance = Number(contact.balance);
    if (balance > 0) return <ArrowUpRight className="text-blue-500" />;
    if (balance < 0) return <ArrowDownRight className="text-red-500" />;
    return <Minus className="text-green-500" />;
  };

  const renderLocations = (
    locations: contactLoaction[],
    type: "shared" | "owned",
  ) => (
    <div className="space-y-2">
      {locations.slice(0, 3).map((loc, i) => (
        <div
          key={i}
          className="rounded-md bg-gray-100 p-2 dark:bg-gray-700 dark:text-gray-100"
        >
          {loc.name} - {loc.address}
        </div>
      ))}
      {locations.length > 3 && (
        <button
          onClick={() => setLocationModalType(type)}
          className="text-sm text-blue-600 hover:underline dark:text-blue-400"
        >
          {t("viewMore")} ({locations.length - 3})
        </button>
      )}
    </div>
  );

  const handleDelete = async () => {
    if (contact && contact.id) {
      setIsDeleting(true);
      setDeleteError(null);


      try {
        await contactService.softDeleteContact(contact.id);
        
        setMessage(t("contactDeletedSuccessfully"));
        setSuccessPopup(true);
        onDelete();

        setIsDeleteModalOpen(false); // Close modal
      } catch (error: any) {
        setMessage(t("errorDeletingContact"));
        setErrorPopup(true);
        console.error("Error deleting contact:", error);
        // Handle error response for the modal display
        if (error.response && error.response.data) {
          const errorMessage =
            error.response.data.details ||
            error.response.data.error ||
            error.response.data.warning ||
            error.response.data.message ||
            "Error deleting contact";
          setDeleteError(errorMessage);
        } else {
          setDeleteError("Failed to delete contact. Please try again.");
        }
      } finally {
        setIsDeleting(false);
      }
    }
  };

  return (
    <>
    {isSuccessPopup && (
      <SuccessPopup
        message={message}
        onClose={() => setSuccessPopup(false)}
      />
    )}
    {isErrorPopup && (
      <ErrorPopup
        message={message}
        onClose={() => setErrorPopup(false)}
      />
    )}



    <div className="relative w-full">
      <Card className="relative w-full">
        <button
          onClick={onClose}
          className={`absolute ${
            language === "ar" ? "left-4" : "right-4"
          } top-4 text-gray-400 hover:text-gray-600 dark:text-gray-500 dark:hover:text-white`}
        >
          <X size={24} />
        </button>
        <CardHeader className="border-b dark:border-gray-700">
          <CardTitle className="text-xl font-semibold text-gray-900 dark:text-white">
            {t("contactDetails")}
          </CardTitle>
        </CardHeader>

        <CardContent className="space-y-8 pt-6">
          {/* Balance and Identity */}
          <div className="flex items-start gap-4 rounded-lg bg-gray-50 p-4 dark:bg-gray-800/50">
            {getBalanceIndicator()}
            <div className="flex-1">
              <div className="flex items-center justify-between">
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                    {contact.name}{" "}
                    <span className="ml-2 rounded-full bg-blue-100 px-2 py-0.5 text-sm text-blue-800 dark:bg-blue-800 dark:text-white">
                      {contact.type}
                    </span>
                  </h3>
                  <p className="text-gray-600 dark:text-gray-300">
                    {contact.company}
                  </p>
                </div>

                <div
                  className={`text-2xl font-bold ${
                    contact.balance > 0
                      ? "text-blue-600"
                      : contact.balance < 0
                        ? "text-red-600"
                        : "text-green-600"
                  }`}
                >
                    {Number(contact.balance).toLocaleString(
                      language === "ar" ? "ar-EG" : "en-US",
                      {
                      minimumFractionDigits: 0,
                      maximumFractionDigits: 0,
                      },
                    )}{" "}
                    {t("EGP")}
                </div>
                {/* Actions */}
                <div className="flex justify-end gap-3">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={onEdit}
                    className="border-blue-200 text-blue-600 dark:border-blue-800 dark:hover:bg-blue-900/20"
                  >
                    <Edit2 className="mr-1.5 h-4 w-4" />
                    {t("edit")}
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setIsDeleteModalOpen(true)}
                    className="border-red-200 text-red-600 dark:border-red-800 dark:hover:bg-red-900/20"
                  >
                    <Trash2 className="mr-1.5 h-4 w-4" />
                    {t("delete")}
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => {
                      generatePDF(
                        contactEvents,
                        language,
                        t,
                        contact.name,
                        contact,
                        undefined,
                        undefined,
                        undefined,
                         contactReservations,
                      );
                    }}
                    className="border-gray-200 text-gray-600 dark:border-gray-800 dark:hover:bg-gray-900/20"
                  >
                    <ArrowUpRight className="ml-1.5 h-4 w-4" />
                    {t("generatePDF")}
                  </Button>
                </div>
              </div>
            </div>
          </div>

          {/* Contact Info */}
          <section>
            {contact.email || contact.phone || contact.address ? (
              <h4 className="mb-4 text-sm font-medium text-gray-900 dark:text-white">
              {t("contactInformation")}
              </h4>
            ) : null}
            <div className="grid grid-cols-1 gap-6 md:grid-cols-3">
              {[
                {
                  icon: <Mail className="text-blue-500" />,
                  label: "email",
                  value: contact.email,
                },
                {
                  icon: <Phone className="text-green-500" />,
                  label: "phone",
                  value: contact.phone,
                },
                {
                  icon: <MapPin className="text-red-500" />,
                  label: "address",
                  value: contact.address,
                },
              ]
                .filter((item) => item.value)
                .map((item, idx) => (
                  <div key={idx} className="flex items-start gap-3">
                    {React.cloneElement(item.icon, {
                      size: 20,
                      className: `${item.icon.props.className} mt-1`,
                    })}
                    <div>
                      <p className="text-sm font-medium text-gray-500 dark:text-gray-400">
                        {t(item.label)}
                      </p>
                      <p className="text-gray-900 dark:text-gray-100">
                        {item.value}
                      </p>
                    </div>
                  </div>
                ))}
            </div>
          </section>

          {/* Locations */}
          <section className="grid grid-cols-1 gap-6 md:grid-cols-2">
            {contact.ownedLocations.length > 0 && (
              <div>
                <h4 className="mb-2 text-sm font-medium text-gray-900 dark:text-white">
                  {t("ownedLocations")}
                </h4>
                {renderLocations(contact.ownedLocations, "owned")}
              </div>
            )}
            {contact.sharedLocations.length > 0 && (
              <div>
                <h4 className="mb-2 text-sm font-medium text-gray-900 dark:text-white">
                  {t("sharedLocations")}
                </h4>
                {renderLocations(contact.sharedLocations, "shared")}
              </div>
            )}
          </section>
        </CardContent>
        {/* Reservations Section */}
        {contactReservations.length > 0 && (
          <CardContent className="space-y-8 pt-6">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
          {t("reservations")}
              </h3>
                <div className="relative">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setShowReservations((prev) => !prev)}
                  className="border-gray-200 text-gray-600 dark:border-gray-800 dark:hover:bg-gray-900/20"
                >
                  {showReservations ? t("hide") : t("show")}
                </Button>

                </div>
            </div>
            {showReservations && (
              <ReservationList
                props_reservations={contactReservations}
                onDeleteReservation={(id: number) => {
                  setContactReservations((prev) =>
                    prev.filter((res) => res.id !== String(id)),
                  );
                }}
              />
            )}
          </CardContent>
        )}

        {/* Financial Data Section */}
        {contactEvents.length > 0 && (
          <CardContent className="space-y-8 pt-6">
            <div className="flex items-center justify-between mb-4">
            {/* Title for Financial Data */}
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
              {t("financialData")}
            </h3>
            {/* Toggle for Financial Data */}
            
              <div className="flex items-center gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setShowFinances((prev) => !prev)}
                  className="border-gray-200 text-gray-600 dark:border-gray-800 dark:hover:bg-gray-900/20"
                >
                  {showFinances ? t("hide") : t("show")}
                </Button>
              </div>
              </div>
              
            {showFinances && (
              <>
            {/* Tabs for Incomes, Expenses, and All Events */}
            <div className="flex border-b border-gray-200 dark:border-gray-700">
              <button
                onClick={() => setActiveTab("all")}
                className={`px-4 py-2 text-sm font-medium ${
                  activeTab === "all"
                    ? "border-b-2 border-blue-500 text-blue-600"
                    : "text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
                }`}
              >
                {t("allEvents")}
              </button>
              <button
                onClick={() => setActiveTab("incomes")}
                className={`px-4 py-2 text-sm font-medium ${
                  activeTab === "incomes"
                    ? "border-b-2 border-blue-500 text-blue-600"
                    : "text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
                }`}
              >
                {t("incomes")}
              </button>
              <button
                onClick={() => setActiveTab("expenses")}
                className={`px-4 py-2 text-sm font-medium ${
                  activeTab === "expenses"
                    ? "border-b-2 border-blue-500 text-blue-600"
                    : "text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
                }`}
              >
                {t("expenses")}
              </button>
            </div>

            {/* Upcoming Events Page */}
            {contactEvents.length > 0 && (
              <div className="mt-4">
                <UpcomingEventsPage
                  events={contactEvents}
                  setEvents={setContactEvents}
                  mode={
                    activeTab === "incomes"
                      ? "income"
                      : activeTab === "expenses"
                        ? "expenses"
                        : "overview"
                  }
                  title=" "
                />
              </div>
            )}
            </>
            )}
          </CardContent>

        )}
      </Card>

      {/* Location Modal */}
      {locationModalType && (
        <LocationModal
          locations={(locationModalType === "owned"
            ? contact.ownedLocations
            : contact.sharedLocations
          ).map((l) => `${l.name} - ${l.address}`)}
          title={
            locationModalType === "owned"
              ? t("ownedLocations")
              : t("sharedLocations")
          }
          onClose={() => setLocationModalType(null)}
        />
      )}

      {/* Delete Confirmation Modal */}
      <DeleteConfirmationModal
        isOpen={isDeleteModalOpen}
        onClose={() => {
          setIsDeleteModalOpen(false);
          setDeleteError(null);
        }}
        onConfirm={handleDelete}
        message={`${t("deleteContactWarning")}`}
        conditions={[
          t("deleteContactConditionsNote"),
          t("deleteContactConditionsOwnership"),
          t("deleteContactConditionsIncomeEvents"),
          t("deleteContactConditionsExpenseEvents"),
          t("deleteContactConditionsContractsReservations"),
        ]}
        title={t("deleteContact")}
        itemName={contact?.name || ""}
        isLoading={isDeleting}
        error={deleteError}
      />
    </div>
    </>
  );
};

export default ContactDetails;
